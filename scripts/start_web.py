#!/usr/bin/env python3
"""
Web界面启动脚本

快速启动Web聊天界面
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from tts_demo.config import get_app_config
import uvicorn


def main():
    """启动Web界面"""
    config = get_app_config()

    print(f"启动Web界面...")
    print(f"访问地址: http://{config.web.host}:{config.web.port}")

    uvicorn.run(
        "tts_demo.web.main:app",
        host=config.web.host,
        port=config.web.port,
        reload=config.web.reload,
        log_level=config.web.log_level,
        reload_dirs=[str(Path(__file__).parent.parent / "src")] if config.web.reload else None
    )


if __name__ == "__main__":
    main()
