#!/usr/bin/env python3
"""
TTS Demo 统一启动脚本

提供多种启动方式：
- 命令行TTS演示
- Web聊天界面
- 配置管理
"""

import sys
import argparse
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from tts_demo.main import main as demo_main
from tts_demo.config import get_app_config, save_config_file
import uvicorn


def run_demo():
    """运行TTS演示"""
    # 直接调用main函数，不再通过命令行参数传递
    return demo_main()


def run_web(host: str = None, port: int = None, reload: bool = None):
    """运行Web界面"""
    config = get_app_config()

    # 使用参数覆盖配置
    if host is not None:
        config.web.host = host
    if port is not None:
        config.web.port = port
    if reload is not None:
        config.web.reload = reload

    # 使用导入字符串启动，支持热重载
    uvicorn.run(
        "tts_demo.web.main:app",
        host=config.web.host,
        port=config.web.port,
        reload=config.web.reload,
        log_level=config.web.log_level,
        reload_dirs=[str(Path(__file__).parent.parent / "src")] if config.web.reload else None
    )


def show_config():
    """显示当前配置"""
    config = get_app_config()
    print("当前配置:")
    print("=" * 50)

    print(f"LLM配置:")
    print(f"  模型: {config.llm.model}")
    print(f"  API密钥: {'已设置' if config.llm.api_key else '未设置'}")
    print(f"  基础URL: {config.llm.base_url or '默认'}")

    print(f"\nLangflow配置:")
    print(f"  服务器URL: {config.langflow.base_url}")
    print(f"  API密钥: {'已设置' if config.langflow.api_key else '未设置'}")
    print(f"  工作流ID: {config.langflow.flow_id or '未设置'}")

    print(f"\nTTS配置:")
    print(f"  语音: {config.tts.voice}")
    print(f"  语速: {config.tts.rate}")
    print(f"  音量: {config.tts.volume}")

    print(f"\n分块配置:")
    print(f"  首块断句模式: {config.chunking.first_chunk_break_mode}")
    print(f"  首块大小: {config.chunking.first_chunk_size}")
    print(f"  最小句子大小: {config.chunking.min_sentence_size}")
    print(f"  最大块大小: {config.chunking.max_chunk_size}")

    print(f"\nWeb配置:")
    print(f"  主机: {config.web.host}")
    print(f"  端口: {config.web.port}")
    print(f"  热重载: {config.web.reload}")


def save_default_config():
    """保存默认配置到文件"""
    config = get_app_config()
    save_config_file(config)
    print("默认配置已保存到 config/app.json")


def main():
    """主入口函数"""
    parser = argparse.ArgumentParser(
        description="TTS Demo 统一启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s demo --prompt "你是?"     # 运行TTS演示
  %(prog)s web --port 8080                # 启动Web界面
  %(prog)s config                         # 显示当前配置
  %(prog)s save-config                    # 保存默认配置
        """
    )

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # Demo命令
    demo_parser = subparsers.add_parser("demo", help="运行TTS演示")
    demo_parser.add_argument("--prompt", help="发送给大模型的提示词")
    demo_parser.add_argument("--config", help="配置文件路径")

    # 手动添加demo的主要参数
    demo_parser.add_argument("--model", help="使用的模型名称")
    demo_parser.add_argument("--api-key", help="OpenAI API密钥")
    demo_parser.add_argument("--base-url", help="OpenAI API基础URL")
    demo_parser.add_argument("--use-langflow", action="store_true", help="使用Langflow工作流")
    demo_parser.add_argument("--voice", help="TTS语音名称")
    demo_parser.add_argument("--rate", help="语速")
    demo_parser.add_argument("--volume", help="音量")
    demo_parser.add_argument("--debug", action="store_true", help="启用调试模式")

    # Web命令
    web_parser = subparsers.add_parser("web", help="启动Web界面")
    web_parser.add_argument("--host", default="0.0.0.0", help="服务器主机")
    web_parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    web_parser.add_argument("--no-reload", action="store_true", help="禁用热重载")

    # 配置命令
    config_parser = subparsers.add_parser("config", help="显示当前配置")

    # 保存配置命令
    save_config_parser = subparsers.add_parser("save-config", help="保存默认配置到文件")

    args = parser.parse_args()

    if args.command == "demo":
        # 直接调用main函数，传递参数
        from tts_demo.main import run_llm_tts_demo
        import asyncio
        
        # 准备参数
        kwargs = {}
        for arg_name, arg_value in vars(args).items():
            if arg_name not in ['command'] and arg_value is not None:
                # 转换参数名格式
                param_name = arg_name.replace('-', '_')
                kwargs[param_name] = arg_value
        
        try:
            # 运行主程序
            return asyncio.run(run_llm_tts_demo(**kwargs))
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行错误: {e}")
            return 1

    elif args.command == "web":
        run_web(
            host=args.host,
            port=args.port,
            reload=not args.no_reload
        )

    elif args.command == "config":
        show_config()

    elif args.command == "save-config":
        save_default_config()

    else:
        parser.print_help()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
