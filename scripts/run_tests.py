#!/usr/bin/env python3
"""
测试运行脚本

运行项目的主流程测试，确保核心功能正常工作
"""

import sys
import subprocess
from pathlib import Path


def run_main_flow_tests():
    """运行主流程测试"""
    print("运行 TTS Demo 主流程测试...")
    print("=" * 50)
    
    test_file = Path(__file__).parent.parent / "tests" / "test_main_flow.py"
    
    try:
        result = subprocess.run([
            sys.executable, str(test_file)
        ], capture_output=True, text=True, cwd=Path(__file__).parent.parent)
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"运行测试失败: {e}")
        return False


def run_pytest_if_available():
    """如果pytest可用，运行pytest"""
    try:
        import pytest
        print("\n使用 pytest 运行测试...")
        print("=" * 50)
        
        test_dir = Path(__file__).parent.parent / "tests"
        result = subprocess.run([
            sys.executable, "-m", "pytest", str(test_dir), "-v"
        ], cwd=Path(__file__).parent.parent)
        
        return result.returncode == 0
    except ImportError:
        print("\npytest 不可用，跳过 pytest 测试")
        return True


def main():
    """主函数"""
    print("TTS Demo 测试运行器")
    print("=" * 50)
    
    # 运行主流程测试
    main_tests_ok = run_main_flow_tests()
    
    # 尝试运行pytest
    pytest_ok = run_pytest_if_available()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"主流程测试: {'✓ 通过' if main_tests_ok else '✗ 失败'}")
    print(f"pytest测试: {'✓ 通过' if pytest_ok else '✗ 失败'}")
    
    if main_tests_ok and pytest_ok:
        print("\n🎉 所有测试通过！")
        return 0
    else:
        print("\n❌ 部分测试失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
