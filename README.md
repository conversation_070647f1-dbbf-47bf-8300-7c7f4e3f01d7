# TTS Demo - 大模型流式TTS演示程序

将大模型的流式文本输出实时转换为语音的演示程序，支持多种LLM源和TTS引擎。

## 功能特性

- 🎯 **实时流式TTS**: 将大模型的流式输出实时转换为语音
- 🤖 **多LLM支持**: 支持OpenAI API和Langflow工作流
- 🔊 **多TTS引擎**: 支持Edge TTS、gTTS、pyttsx3
- 🌐 **Web界面**: 基于FastAPI的实时聊天界面
- ⚙️ **配置管理**: 统一的配置管理系统
- 📊 **性能监控**: 详细的性能统计和监控
- 🎛️ **高度可配置**: 支持语音、语速、分块等多种参数调整

## 快速开始

### 1. 安装依赖

本项目使用 [uv](https://docs.astral.sh/uv/) 作为依赖管理工具。

```bash
# 安装uv（如果尚未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装项目依赖
uv sync

```

### 2. 配置环境变量

创建 `.env` 文件：

```bash
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# Langflow配置（可选）
LANGFLOW_BASE_URL=http://***********:37860
LANGFLOW_API_KEY=your_langflow_api_key
LANGFLOW_FLOW_ID=your_flow_id

# TTS配置
DEFAULT_TTS_VOICE=zh-CN-YunyangNeural
DEFAULT_TTS_RATE=+25%
DEFAULT_TTS_VOLUME=+0%

# LLM配置
DEFAULT_LLM_MODEL=gpt-4o-mini
```

### 3. 运行演示

#### 命令行模式

```bash
# 基本使用
uv run python scripts/run_demo.py demo --prompt "你是？"

# 使用Langflow
uv run python scripts/run_demo.py demo --use-langflow --prompt "你好"

# 自定义参数
uv run python scripts/run_demo.py demo \
  --prompt "介绍机器学习" \
  --voice "zh-CN-XiaoxiaoNeural" \
  --rate "+50%"
```

#### Web界面模式

```bash
# 启动Web界面（默认启用热重载）
uv run python scripts/run_demo.py web

# 自定义端口
uv run python scripts/run_demo.py web --port 8080

# 禁用热重载（生产环境推荐）
uv run python scripts/run_demo.py web --no-reload

# 或直接使用uvicorn（支持热重载）
uv run uvicorn tts_demo.web.main:app --reload --host 0.0.0.0 --port 8000
```

然后访问 http://localhost:8000

## 配置管理

### 查看当前配置
```bash
uv run python scripts/run_demo.py config
```

### 保存默认配置
```bash
uv run python scripts/run_demo.py save-config
```

配置文件将保存到 `config/app.json`

## 项目结构

```
TTS_demo/
├── src/tts_demo/          # 主要源代码
│   ├── config/            # 配置管理
│   ├── llm/              # LLM客户端
│   ├── speaker/          # TTS引擎
│   ├── web/              # Web界面
│   └── main.py           # 主程序
├── scripts/              # 启动脚本
│   ├── run_demo.py       # 统一启动脚本
│   └── start_web.py      # Web快速启动
├── tests/                # 测试文件
├── examples/             # 示例代码
├── docs/                 # 项目文档
├── config/               # 配置文件
└── pyproject.toml        # 项目配置
```

## 使用示例

### 基本TTS演示

```python
from tts_demo import run_llm_tts_demo
import asyncio

async def main():
    await run_llm_tts_demo(
        prompt="你是?",
        voice="zh-CN-YunyangNeural",
        rate="+25%"
    )

asyncio.run(main())
```

### Web界面集成

```python
from tts_demo.web import create_app
from tts_demo.config import get_app_config
import uvicorn

config = get_app_config()
app = create_app(config.web)

uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 命令行参数

### 基本参数
- `--prompt`: 发送给大模型的提示词
- `--config`: 配置文件路径
- `--model`: 使用的模型名称
- `--api-key`: OpenAI API密钥
- `--base-url`: OpenAI API基础URL

### LLM配置
- `--use-langflow`: 使用Langflow工作流
- `--langflow-base-url`: Langflow服务器URL
- `--langflow-api-key`: Langflow API密钥
- `--langflow-flow-id`: Langflow工作流ID

### TTS配置
- `--voice`: TTS语音名称
- `--rate`: 语速（如 "+25%"）
- `--volume`: 音量（如 "+0%"）

### 其他选项
- `--debug`: 启用调试模式

## 支持的语音

常用的中文语音包括：
- `zh-CN-YunyangNeural`: 云扬（男声）
- `zh-CN-XiaoxiaoNeural`: 晓晓（女声）
- `zh-CN-YunjianNeural`: 云健（男声）
- `zh-CN-XiaoyiNeural`: 晓伊（女声）

## 故障排除

### 常见问题

1. **音频播放问题**: 确保系统支持音频播放，检查pygame安装
2. **API连接问题**: 检查网络连接和API密钥配置
3. **依赖安装问题**:
   ```bash
   # 重新安装依赖
   uv sync --reinstall

   # 清理缓存后重新安装
   uv cache clean
   uv sync
   ```

### 调试模式

```bash
# 启用调试模式
uv run python scripts/run_demo.py demo --debug --prompt "测试"
```

## 测试

### 运行测试

```bash
# 运行主流程测试
uv run python tests/test_main_flow.py

# 或使用测试脚本
uv run python scripts/run_tests.py

# 使用pytest（如果已安装）
pytest tests/ -v
```

### 测试覆盖

项目测试专注于主流程覆盖：
- 项目结构验证
- 核心模块导入
- 配置系统功能
- LLM客户端创建
- TTS组件创建
- Web应用创建
- 主要入口点

## 开发指南

### 依赖管理

项目使用 **uv** 进行依赖管理：

```bash
# 添加新依赖
uv add package_name

# 添加开发依赖
uv add --dev package_name

# 移除依赖
uv remove package_name

# 更新依赖
uv sync --upgrade

# 查看依赖树
uv tree
```

**注意**: 项目包含 `uv.lock` 文件，它锁定了所有依赖的精确版本，确保在不同环境中的一致性。请不要手动编辑此文件。

### 项目结构

项目采用标准Python包结构，主要模块：

- `src/tts_demo/config/`: 配置管理
- `src/tts_demo/llm/`: LLM客户端
- `src/tts_demo/speaker/`: TTS引擎
- `src/tts_demo/web/`: Web界面

## 许可证

MIT License

## 更新日志

### v0.2.0 (当前版本)
- 重构项目结构，提高代码组织性
- 添加统一配置管理系统
- 改进Web界面功能
- 优化性能和稳定性
- 统一启动脚本

### v0.1.0
- 初始版本发布
- 基本TTS功能实现
- 支持OpenAI和Langflow

---

**使用新的启动方式**: `python scripts/run_demo.py`