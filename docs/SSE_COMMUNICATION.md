# SSE通信功能说明

## 概述

项目现在支持两种通信方式：
1. **WebSocket** - 双向实时通信（默认）
2. **Server-Sent Events (SSE)** - 单向服务器推送

## 功能特性

### WebSocket模式
- 双向实时通信
- 低延迟
- 支持二进制数据
- 自动重连机制

### SSE模式
- 单向服务器推送
- 基于HTTP协议
- 自动重连
- 更好的防火墙兼容性
- 支持HTTP/2多路复用

## 配置方式

### 1. 全局配置
在配置文件中设置默认通信方式：

```json
{
  "web": {
    "communication_mode": "websocket"  // 或 "sse"
  }
}
```

### 2. 会话级配置
在Web界面的配置面板中可以为每个会话单独设置通信方式：

1. 打开聊天界面
2. 点击右上角的"配置"按钮
3. 在"高级配置"部分选择通信方式
4. 点击"保存配置"

## API端点

### WebSocket端点
```
ws://localhost:8000/ws/{session_id}
```

### SSE端点
```
GET http://localhost:8000/sse/{session_id}
```

### 消息发送API（SSE模式专用）
```
POST http://localhost:8000/api/sessions/{session_id}/send
Content-Type: application/json

{
  "content": "消息内容"
}
```

## 消息格式

两种通信方式使用相同的消息格式：

### 客户端到服务器（仅WebSocket）
```json
{
  "type": "chat",
  "content": "用户消息内容"
}
```

### 服务器到客户端
```json
{
  "type": "message|start|token|end|error|heartbeat",
  "content": "消息内容",
  "message_id": "消息ID",
  "message": {
    "id": "消息ID",
    "role": "user|assistant",
    "content": "消息内容",
    "timestamp": "时间戳",
    "session_id": "会话ID"
  }
}
```

## 使用示例

### JavaScript客户端示例

#### WebSocket模式
```javascript
const websocket = new WebSocket(`ws://localhost:8000/ws/${sessionId}`);

websocket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    handleMessage(data);
};

// 发送消息
websocket.send(JSON.stringify({
    type: 'chat',
    content: '你好'
}));
```

#### SSE模式
```javascript
const eventSource = new EventSource(`/sse/${sessionId}`);

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type !== 'heartbeat') {
        handleMessage(data);
    }
};

// 发送消息
fetch(`/api/sessions/${sessionId}/send`, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        content: '你好'
    })
});
```

## 切换通信方式

### 运行时切换
1. 在Web界面配置面板中选择新的通信方式
2. 点击"保存配置"
3. 系统会自动断开当前连接并建立新的连接

### 程序化切换
```javascript
// 更新配置
const config = {
    // ... 其他配置
    communication_mode: 'sse'  // 或 'websocket'
};

fetch(`/api/sessions/${sessionId}/config`, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(config)
});
```

## 测试和演示

### 1. 运行测试脚本
```bash
# 启动Web服务器
uv run scripts/start_web.py

# 在另一个终端运行测试
python test_sse.py
```

### 2. 使用演示页面
1. 启动Web服务器
2. 在浏览器中打开 `demo_sse.html`
3. 选择通信方式并测试

## 注意事项

1. **SSE限制**：SSE是单向通信，客户端无法直接通过SSE连接发送消息，需要使用HTTP API
2. **浏览器限制**：某些浏览器对同一域名的SSE连接数有限制（通常6个）
3. **代理服务器**：某些代理服务器可能会缓冲SSE响应，影响实时性
4. **重连机制**：两种模式都实现了自动重连，但重连策略略有不同

## 性能对比

| 特性 | WebSocket | SSE |
|------|-----------|-----|
| 延迟 | 极低 | 低 |
| 服务器资源 | 中等 | 较低 |
| 客户端兼容性 | 好 | 很好 |
| 防火墙友好性 | 一般 | 好 |
| 实现复杂度 | 中等 | 简单 |

## 故障排除

### 常见问题

1. **SSE连接失败**
   - 检查服务器是否正在运行
   - 确认端点URL正确
   - 检查浏览器控制台错误

2. **消息发送失败**
   - 确认使用正确的API端点
   - 检查请求格式和Content-Type
   - 验证会话ID是否有效

3. **自动重连不工作**
   - 检查网络连接
   - 确认重连逻辑没有被禁用
   - 查看浏览器控制台日志
