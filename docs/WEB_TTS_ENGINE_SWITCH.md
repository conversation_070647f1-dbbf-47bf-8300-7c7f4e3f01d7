# Web界面TTS引擎切换功能

## 🎯 功能概述

为TTS Demo项目的Web界面添加了完整的TTS引擎切换功能，用户可以在聊天界面中实时切换不同的TTS引擎，体验不同的语音合成效果。

## ✨ 新增功能

### 1. **TTS引擎选择器**
- **位置**: 配置面板 > TTS配置组
- **功能**: 下拉选择框，支持5种主流TTS引擎
- **选项**:
  - Microsoft Edge TTS (免费)
  - F5-TTS (开源)
  - MegaTTS3 (需配置)
  - CosyVoice2 (开源)
  - OpenAI TTS (需API密钥)

### 2. **引擎状态显示**
- **实时状态**: 显示当前引擎的运行状态
- **状态类型**:
  - ✅ 已就绪 (绿色)
  - ⏳ 切换中 (黄色)
  - ❌ 错误 (红色)
  - ⚙️ 需配置 (黄色)

### 3. **一键切换按钮**
- **功能**: 点击即可切换到选定的TTS引擎
- **反馈**: 显示切换进度和结果
- **自动保存**: 切换成功后自动保存配置

### 4. **引擎特定配置**
根据选择的引擎动态显示相应的配置选项：

#### **OpenAI TTS配置**
- API密钥 (必填)
- 模型选择 (tts-1 / tts-1-hd)

#### **MegaTTS3配置**
- API地址
- API密钥
- 模型名称

#### **F5-TTS配置**
- 设备选择 (自动/CPU/CUDA)
- 模型路径 (可选)

#### **CosyVoice2配置**
- 说话人选择 (中文女声1/2, 中文男声1/2)
- 情感选择 (中性/开心/悲伤/愤怒)
- 设备选择 (自动/CPU/CUDA)

#### **Edge TTS配置**
- 无需额外配置

## 🔧 技术实现

### 前端实现

#### **HTML结构**
```html
<!-- TTS引擎选择 -->
<div class="mb-3">
    <label class="form-label form-label-sm">TTS引擎</label>
    <select class="form-select form-select-sm" id="ttsEngine">
        <option value="edge_tts">Microsoft Edge TTS (免费)</option>
        <option value="f5_tts">F5-TTS (开源)</option>
        <!-- 更多选项... -->
    </select>
</div>

<!-- 引擎状态显示 -->
<div class="mb-2">
    <div class="d-flex align-items-center">
        <span class="badge bg-success me-2" id="engineStatus">
            <i class="bi bi-check-circle"></i> 已就绪
        </span>
        <button class="btn btn-outline-primary btn-sm" onclick="switchTTSEngine()">
            <i class="bi bi-arrow-repeat"></i> 切换引擎
        </button>
    </div>
</div>

<!-- 引擎特定配置区域 -->
<div id="engineSpecificConfig">
    <!-- 动态生成的配置内容 -->
</div>
```

#### **JavaScript核心函数**

1. **引擎切换函数**
```javascript
async function switchTTSEngine() {
    const selectedEngine = document.getElementById('ttsEngine').value;
    
    const response = await fetch(`/api/sessions/${sessionId}/tts/switch-engine`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ engine_type: selectedEngine })
    });
    
    const result = await response.json();
    // 处理切换结果...
}
```

2. **状态更新函数**
```javascript
function updateEngineStatus(engineType, status = 'ready') {
    const statusBadge = document.getElementById('engineStatus');
    
    switch (status) {
        case 'ready':
            statusBadge.className = 'badge bg-success me-2';
            statusBadge.innerHTML = '<i class="bi bi-check-circle"></i> 已就绪';
            break;
        // 其他状态...
    }
}
```

3. **动态配置渲染**
```javascript
function renderEngineSpecificConfig(engineType, config = {}) {
    const configContainer = document.getElementById('engineSpecificConfig');
    
    switch (engineType) {
        case 'openai_tts':
            configContainer.innerHTML = `
                <div class="mb-2">
                    <label>OpenAI API密钥</label>
                    <input type="password" id="openaiTtsApiKey" ...>
                </div>
            `;
            break;
        // 其他引擎配置...
    }
}
```

### 后端支持

#### **配置模型扩展**
```python
@dataclass
class ChatConfig:
    # 新增TTS引擎配置
    tts_engine: str = "edge_tts"
    
    # 引擎特定配置
    edge_tts_config: Optional[Dict] = None
    f5_tts_config: Optional[Dict] = None
    megatts3_config: Optional[Dict] = None
    cosyvoice2_config: Optional[Dict] = None
    openai_tts_config: Optional[Dict] = None
```

#### **API端点**
- `GET /api/tts/engines` - 获取可用引擎列表
- `POST /api/sessions/{session_id}/tts/switch-engine` - 切换引擎
- `GET /api/sessions/{session_id}/tts/status` - 获取引擎状态

## 📁 目录结构分析

### **speaker目录 vs audio目录**

#### **speaker目录** - 高级TTS处理器
- **用途**: 提供完整的TTS处理流程
- **特点**: 面向应用层，提供易用的API接口
- **核心文件**: 
  - ✅ `stream_speaker.py` - 现代化流式TTS处理器 (仍在使用)
  - ❌ `edge_speaker.py` - 已删除 (过时的简单封装)
  - ❌ `gtts_speaker.py` - 已删除 (过时的Google TTS封装)
  - ❌ `tts_speaker.py` - 已删除 (过时的pyttsx3封装)

#### **audio目录** - 底层音频生成器
- **用途**: 提供底层的音频生成功能
- **特点**: 面向引擎层，实现AudioGenerator接口
- **核心文件**:
  - ✅ `engine_factory.py` - 引擎工厂和管理器
  - ✅ `edge_tts_generator.py` - Edge TTS音频生成器
  - ✅ `f5_tts_generator.py` - F5-TTS音频生成器
  - ✅ `megatts3_generator.py` - MegaTTS3音频生成器
  - ✅ `cosyvoice2_generator.py` - CosyVoice2音频生成器
  - ✅ `openai_tts_generator.py` - OpenAI TTS音频生成器

### **架构层次**
```
Web界面 (chat.html)
    ↓
ChatManager (app.py)
    ↓
StreamSpeaker (stream_speaker.py)
    ↓
TTSEngineManager (engine_factory.py)
    ↓
具体引擎实现 (各种_generator.py)
```

## 🚀 使用方法

### 1. **基本使用**
1. 打开聊天界面
2. 点击右侧"配置"按钮
3. 在TTS配置组中选择引擎
4. 点击"切换引擎"按钮
5. 配置引擎特定参数（如需要）
6. 点击"测试语音"验证效果

### 2. **引擎配置示例**

#### **配置OpenAI TTS**
1. 选择"OpenAI TTS (需API密钥)"
2. 输入有效的OpenAI API密钥
3. 选择模型 (tts-1 或 tts-1-hd)
4. 点击"切换引擎"
5. 测试语音效果

#### **配置CosyVoice2**
1. 选择"CosyVoice2 (开源)"
2. 选择说话人 (如"中文女声1")
3. 选择情感 (如"开心")
4. 选择设备 (推荐"自动选择")
5. 点击"切换引擎"

### 3. **故障排除**

#### **切换失败**
- 检查网络连接
- 验证API密钥有效性
- 查看浏览器控制台错误信息
- 检查服务器日志

#### **配置不生效**
- 确保点击了"切换引擎"按钮
- 检查配置是否已保存
- 刷新页面重新加载配置

## 🎯 用户体验优化

### 1. **视觉反馈**
- 实时状态指示器
- 切换进度显示
- 成功/失败提示消息
- 配置验证提示

### 2. **交互优化**
- 自动保存配置
- 防抖处理避免频繁请求
- 按钮状态管理
- 错误恢复机制

### 3. **性能优化**
- 动态配置渲染
- 延迟加载引擎
- 智能缓存机制
- 资源清理

## 🔮 未来扩展

### 1. **功能增强**
- [ ] 引擎性能监控
- [ ] 语音质量评估
- [ ] 批量引擎测试
- [ ] 自定义引擎插件

### 2. **用户体验**
- [ ] 引擎推荐系统
- [ ] 语音样本预览
- [ ] 配置模板保存
- [ ] 快速切换热键

### 3. **技术优化**
- [ ] 引擎预加载
- [ ] 配置验证增强
- [ ] 错误处理优化
- [ ] 性能监控仪表板

## 📋 总结

通过这次更新，TTS Demo项目的Web界面现在支持：

✅ **5种主流TTS引擎**的无缝切换
✅ **实时状态监控**和视觉反馈
✅ **引擎特定配置**的动态管理
✅ **一键切换**和自动保存
✅ **完整的错误处理**和用户提示
✅ **清理过时代码**，优化项目结构

这大大增强了项目的灵活性和用户体验，让用户可以根据具体需求选择最适合的TTS引擎！
