# Web界面布局重新设计

## 设计目标

根据用户需求，重新设计Web界面布局：
1. **不显示垂直滚动条** - 整体布局适应视口高度
2. **会话列表在左侧** - 方便快速切换会话
3. **参数配置在右侧** - 默认收起状态，需要时展开

## 新布局结构

### 三栏布局设计

```
┌─────────────────────────────────────────────────────────────┐
│                        导航栏                                │
├──────────────┬─────────────────────────┬────────────────────┤
│              │                         │                    │
│   会话列表   │       聊天区域          │    配置面板        │
│   (固定宽度) │     (自适应宽度)        │   (可收起)         │
│              │                         │                    │
│              │                         │                    │
│              │                         │                    │
│              │                         │                    │
└──────────────┴─────────────────────────┴────────────────────┘
```

### 布局特点

1. **左侧会话列表**:
   - 固定宽度280px（桌面端）
   - 显示会话预览、消息数量、时间
   - 支持快速切换会话
   - 新建会话按钮

2. **中间聊天区域**:
   - 自适应宽度，占据剩余空间
   - 聊天头部：会话信息和操作按钮
   - 消息区域：可滚动的聊天内容
   - 输入区域：消息输入和发送

3. **右侧配置面板**:
   - 固定宽度320px（桌面端）
   - 默认收起状态（margin-right: -320px）
   - 点击配置按钮展开/收起
   - 包含所有TTS和LLM配置选项

## CSS实现

### 核心布局样式

```css
/* 主容器 */
.chat-container {
    height: 100%;
    overflow: hidden;
    display: flex;
}

/* 左侧会话列表 */
.sessions-sidebar {
    width: 280px;
    height: 100%;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
    flex-shrink: 0;
}

/* 中间聊天区域 */
.chat-main {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 右侧配置面板 */
.config-sidebar {
    width: 320px;
    height: 100%;
    background-color: #f8f9fa;
    border-left: 1px solid #dee2e6;
    overflow-y: auto;
    flex-shrink: 0;
    transition: margin-right 0.3s ease-in-out;
}

.config-sidebar.collapsed {
    margin-right: -320px;
}
```

### 滚动条优化

```css
/* 隐藏滚动条但保持功能 */
.sessions-sidebar::-webkit-scrollbar,
.config-sidebar::-webkit-scrollbar {
    width: 4px;
}

.sessions-sidebar::-webkit-scrollbar-track,
.config-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sessions-sidebar::-webkit-scrollbar-thumb,
.config-sidebar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}
```

### 响应式设计

```css
/* 平板设备 */
@media (max-width: 1200px) {
    .config-sidebar {
        width: 280px;
    }
    .config-sidebar.collapsed {
        margin-right: -280px;
    }
}

/* 手机设备 */
@media (max-width: 768px) {
    .chat-container {
        flex-direction: column;
    }
    
    .sessions-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .config-sidebar {
        width: 100%;
        height: auto;
        max-height: 300px;
        border-left: none;
        border-top: 1px solid #dee2e6;
        margin-right: 0;
    }
    
    .config-sidebar.collapsed {
        margin-right: 0;
        height: 0;
        overflow: hidden;
    }
}
```

## JavaScript功能

### 配置面板切换

```javascript
function toggleConfig() {
    const configSidebar = document.getElementById('configSidebar');
    const configToggle = document.getElementById('configToggle');
    
    configSidebar.classList.toggle('collapsed');
    configToggle.classList.toggle('expanded');
}
```

### 会话列表管理

```javascript
async function loadSessions() {
    try {
        const response = await fetch('/api/sessions');
        const sessions = await response.json();
        
        const sessionsList = document.getElementById('sessionsList');
        sessionsList.innerHTML = '';
        
        sessions.forEach(session => {
            const sessionItem = document.createElement('div');
            sessionItem.className = `session-item ${session.id === sessionId ? 'active' : ''}`;
            sessionItem.onclick = () => openSession(session.id);
            
            const lastMessage = session.last_message || '新会话';
            const preview = lastMessage.length > 30 ? lastMessage.substring(0, 30) + '...' : lastMessage;
            
            sessionItem.innerHTML = `
                <div class="session-title">${preview}</div>
                <div class="session-preview">${session.message_count || 0} 条消息</div>
                <div class="session-time">${new Date(session.timestamp).toLocaleString()}</div>
            `;
            
            sessionsList.appendChild(sessionItem);
        });
    } catch (error) {
        console.error('加载会话列表失败:', error);
    }
}
```

## 用户体验改进

### 1. 无滚动条设计
- 主容器高度固定为视口高度
- 各区域内部独立滚动
- 滚动条样式优化，更加美观

### 2. 直观的会话管理
- 左侧会话列表始终可见
- 当前会话高亮显示
- 一键创建新会话
- 会话预览信息丰富

### 3. 灵活的配置面板
- 默认收起，不占用聊天空间
- 平滑的展开/收起动画
- 配置按钮位置固定，易于访问
- 移动端适配良好

### 4. 响应式适配
- 桌面端：三栏布局
- 平板端：调整侧边栏宽度
- 手机端：垂直堆叠布局

## 技术优势

1. **性能优化**: 使用CSS transform和margin实现动画，性能更好
2. **兼容性**: 支持现代浏览器的flexbox布局
3. **可维护性**: 清晰的CSS类命名和模块化结构
4. **可扩展性**: 易于添加新功能和调整布局

## 总结

新的布局设计完全满足了用户需求：
- ✅ 无垂直滚动条，整体布局适应视口
- ✅ 会话列表在左侧，方便管理
- ✅ 配置面板在右侧，默认收起
- ✅ 响应式设计，适配各种设备
- ✅ 流畅的用户交互体验

这个设计提供了更好的空间利用率和用户体验，同时保持了所有原有功能的完整性。
