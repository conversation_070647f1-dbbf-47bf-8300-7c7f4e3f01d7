# Uvicorn 警告修复

## 问题描述

启动Web界面时出现警告：
```
WARNING: You must pass the application as an import string to enable 'reload' or 'workers'.
```

## 问题原因

这个警告是因为在启动Web应用时，uvicorn无法正确识别应用对象来启用热重载功能。具体原因：

1. **应用对象传递**: 我们直接传递了应用实例给uvicorn，而不是导入字符串
2. **热重载限制**: uvicorn的热重载功能需要通过导入字符串来重新加载模块
3. **模块路径问题**: 缺少可以被uvicorn直接导入的应用入口点

## 解决方案

### 1. 创建专用的Web应用入口

创建了 `src/tts_demo/web/main.py` 文件作为Web应用的主入口：

```python
#!/usr/bin/env python3
"""
Web应用主入口

提供可以被uvicorn直接导入的应用实例，支持热重载功能
"""

import sys
from pathlib import Path

# 确保可以导入tts_demo模块
src_path = Path(__file__).parent.parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from tts_demo.web.app import create_app
from tts_demo.config import get_app_config

# 创建应用实例
config = get_app_config()
app = create_app(config.web)

# 导出应用实例供uvicorn使用
__all__ = ["app"]
```

### 2. 更新启动脚本

修改 `scripts/run_demo.py` 中的 `run_web` 函数：

**修改前**:
```python
def run_web(host: str = None, port: int = None, reload: bool = None):
    app = create_app(config.web)
    
    uvicorn.run(
        app,  # 直接传递应用对象
        host=config.web.host,
        port=config.web.port,
        reload=config.web.reload,
        log_level=config.web.log_level
    )
```

**修改后**:
```python
def run_web(host: str = None, port: int = None, reload: bool = None):
    # 使用导入字符串启动，支持热重载
    uvicorn.run(
        "tts_demo.web.main:app",  # 使用导入字符串
        host=config.web.host,
        port=config.web.port,
        reload=config.web.reload,
        log_level=config.web.log_level,
        reload_dirs=[str(Path(__file__).parent.parent / "src")] if config.web.reload else None
    )
```

### 3. 更新快速启动脚本

同样修改 `scripts/start_web.py`：

```python
def main():
    """启动Web界面"""
    config = get_app_config()
    
    print(f"启动Web界面...")
    print(f"访问地址: http://{config.web.host}:{config.web.port}")
    
    uvicorn.run(
        "tts_demo.web.main:app",
        host=config.web.host,
        port=config.web.port,
        reload=config.web.reload,
        log_level=config.web.log_level,
        reload_dirs=[str(Path(__file__).parent.parent / "src")] if config.web.reload else None
    )
```

## 修复效果

### 修复前
```bash
$ python scripts/run_demo.py web
WARNING: You must pass the application as an import string to enable 'reload' or 'workers'.
INFO: Started server process [12345]
INFO: Waiting for application startup.
INFO: Application startup complete.
INFO: Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
```

### 修复后
```bash
$ python scripts/run_demo.py web
INFO: Will watch for changes in these directories: ['/path/to/TTS_demo/src']
INFO: Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO: Started reloader process [12345] using StatReload
INFO: Started server process [12346]
INFO: Waiting for application startup.
INFO: Application startup complete.
```

## 新功能

### 1. 热重载支持

现在可以正常使用热重载功能：

```bash
# 启用热重载（默认）
uv run python scripts/run_demo.py web

# 禁用热重载
uv run python scripts/run_demo.py web --no-reload
```

### 2. 直接使用uvicorn

也可以直接使用uvicorn启动：

```bash
# 使用uvicorn直接启动
uv run uvicorn tts_demo.web.main:app --reload --host 0.0.0.0 --port 8000

# 指定监控目录
uv run uvicorn tts_demo.web.main:app --reload --reload-dir src/
```

### 3. 生产环境部署

生产环境建议禁用热重载：

```bash
# 生产环境启动
uv run python scripts/run_demo.py web --no-reload --host 0.0.0.0 --port 8000
```

## 技术要点

### 1. 导入字符串格式

uvicorn的导入字符串格式为：`module.path:app_variable`

- `tts_demo.web.main`: 模块路径
- `app`: 应用变量名

### 2. 热重载机制

热重载通过以下方式工作：

1. uvicorn监控指定目录的文件变化
2. 当文件发生变化时，重新导入模块
3. 重新创建应用实例
4. 重启服务器进程

### 3. 路径配置

通过 `reload_dirs` 参数指定监控的目录：

```python
reload_dirs=[str(Path(__file__).parent.parent / "src")]
```

这确保只监控源代码目录，提高性能。

## 最佳实践

### 1. 开发环境

- 启用热重载以提高开发效率
- 监控源代码目录
- 使用调试模式

```bash
uv run python scripts/run_demo.py web --debug
```

### 2. 生产环境

- 禁用热重载以提高性能
- 使用适当的工作进程数
- 配置日志级别

```bash
uv run python scripts/run_demo.py web --no-reload
```

### 3. 容器部署

在Docker容器中部署时：

```dockerfile
CMD ["uvicorn", "tts_demo.web.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 总结

通过创建专用的Web应用入口点和使用导入字符串启动uvicorn，成功解决了警告问题，并获得了以下好处：

1. ✅ **消除警告**: 不再出现uvicorn警告信息
2. ✅ **热重载支持**: 开发时可以正常使用热重载功能
3. ✅ **更好的部署**: 支持多种部署方式
4. ✅ **标准化**: 符合uvicorn的最佳实践
5. ✅ **向后兼容**: 保持所有现有功能正常工作

这个修复提升了开发体验，同时保持了生产环境的稳定性。
