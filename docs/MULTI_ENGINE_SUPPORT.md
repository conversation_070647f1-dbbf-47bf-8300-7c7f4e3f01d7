# TTS多引擎支持功能说明

## 🎯 概述

TTS Demo项目现已支持多种TTS引擎的动态切换，用户可以根据需要选择不同的语音合成引擎，享受不同的语音效果和特性。

## 🔧 支持的TTS引擎

### 1. Microsoft Edge TTS (`edge_tts`)
- **状态**: ✅ 完全支持
- **特点**: 免费、高质量、支持多语言
- **配置**: 无需额外配置
- **优势**: 
  - 完全免费使用
  - 支持多种语言和语音
  - 质量稳定可靠
  - 无需API密钥

### 2. F5-TTS (`f5_tts`)
- **状态**: 🚧 框架已实现（需要模型）
- **特点**: 开源高质量TTS模型，支持自然语音合成
- **配置**: 可选择模型路径和设备
- **优势**:
  - 开源免费
  - 高质量语音合成
  - 支持本地部署
  - 可自定义训练

### 3. MegaTTS3 (`megatts3`)
- **状态**: 🚧 框架已实现（需要API服务）
- **特点**: 高性能TTS模型，支持多语言
- **配置**: 需要API地址和密钥
- **优势**:
  - 高性能处理
  - 支持多语言
  - 商业级质量

### 4. CosyVoice2 (`cosyvoice2`)
- **状态**: 🚧 框架已实现（需要模型）
- **特点**: 阿里巴巴开源TTS，支持情感语音合成
- **配置**: 可选择说话人和情感
- **优势**:
  - 支持情感语音
  - 多说话人选择
  - 开源可定制
  - 中文效果优秀

### 5. OpenAI TTS (`openai_tts`)
- **状态**: ✅ 完全支持
- **特点**: 商业级高质量语音合成服务
- **配置**: 需要API密钥
- **优势**:
  - 商业级质量
  - 稳定可靠
  - 支持多种语音
  - 云端处理

## 🏗️ 架构设计

### 核心组件

1. **TTSEngineType枚举**: 定义所有支持的引擎类型
2. **TTSEngineFactory**: 引擎工厂，负责创建和管理引擎实例
3. **TTSEngineManager**: 引擎管理器，提供引擎切换和回退功能
4. **各引擎实现**: 每个引擎都有独立的实现文件

### 文件结构
```
src/tts_demo/
├── core/models.py              # 添加了TTSEngineType和扩展的TTSConfig
├── audio/
│   ├── engine_factory.py       # 引擎工厂和管理器
│   ├── edge_tts_generator.py   # Edge TTS实现
│   ├── f5_tts_generator.py     # F5-TTS实现
│   ├── megatts3_generator.py   # MegaTTS3实现
│   ├── cosyvoice2_generator.py # CosyVoice2实现
│   ├── openai_tts_generator.py # OpenAI TTS实现
│   └── generators.py           # 统一导出
├── speaker/stream_speaker.py   # 添加了引擎切换方法
└── web/app.py                  # 添加了引擎切换API
```

## 🚀 使用方法

### 1. 编程接口

#### 创建引擎管理器
```python
from src.tts_demo.core.models import TTSConfig, TTSEngineType
from src.tts_demo.audio.engine_factory import create_engine_manager

# 创建配置
config = TTSConfig()
config.engine_type = TTSEngineType.EDGE_TTS
config.fallback_engines = [TTSEngineType.F5_TTS, TTSEngineType.OPENAI_TTS]

# 创建管理器
manager = create_engine_manager(config)

# 获取引擎
engine = await manager.get_engine()

# 切换引擎
success = await manager.switch_engine(TTSEngineType.F5_TTS)
```

#### 使用StreamSpeaker切换引擎
```python
from src.tts_demo.speaker.stream_speaker import StreamSpeaker
from src.tts_demo.core.models import TTSEngineType

# 创建TTS处理器
speaker = StreamSpeaker(voice="zh-CN-YunjianNeural")
await speaker.start()

# 切换引擎
success = await speaker.switch_engine(TTSEngineType.F5_TTS)

# 获取可用引擎
engines = speaker.get_available_engines()
```

### 2. Web API接口

#### 获取可用引擎
```bash
GET /api/tts/engines
```

#### 切换引擎
```bash
POST /api/sessions/{session_id}/tts/switch-engine
Content-Type: application/json

{
    "engine_type": "f5_tts"
}
```

#### 获取TTS状态
```bash
GET /api/sessions/{session_id}/tts/status
```

### 3. Web界面操作

1. 访问聊天界面
2. 在配置面板中选择TTS引擎
3. 配置引擎特定参数（如需要）
4. 保存配置并测试

## ⚙️ 配置说明

### 通用配置
```python
config = TTSConfig(
    engine_type=TTSEngineType.EDGE_TTS,  # 主要引擎
    fallback_engines=[TTSEngineType.F5_TTS],  # 回退引擎
    voice="zh-CN-YunjianNeural",
    rate="+0%",
    volume="+0%"
)
```

### 引擎特定配置

#### OpenAI TTS
```python
config.openai_tts_config = {
    "api_key": "your-api-key",
    "model": "tts-1",  # 或 "tts-1-hd"
    "base_url": "https://api.openai.com/v1"
}
```

#### MegaTTS3
```python
config.megatts3_config = {
    "api_url": "http://localhost:8080/api/tts",
    "api_key": "your-api-key",
    "model_name": "megatts3-base"
}
```

#### F5-TTS
```python
config.f5_tts_config = {
    "model_path": "/path/to/f5-tts-model",
    "device": "cuda"  # 或 "cpu", "auto"
}
```

#### CosyVoice2
```python
config.cosyvoice2_config = {
    "model_path": "/path/to/cosyvoice2-model",
    "speaker_id": "zh-CN-female-1",
    "emotion": "happy",
    "device": "cuda"
}
```

## 🔄 回退机制

系统支持智能回退机制：

1. **主引擎失败**: 自动尝试回退引擎列表中的引擎
2. **配置验证**: 在创建引擎前验证配置完整性
3. **错误处理**: 详细的错误信息和日志记录
4. **资源清理**: 自动清理失败的引擎资源

## 🧪 测试和验证

### 运行测试
```bash
# 基础功能测试
uv run python test_multi_engine.py

# Web界面测试
uv run scripts/start_web.py
# 然后访问 demo_engine_switch.html
```

### 测试内容
- ✅ 引擎工厂创建和注册
- ✅ 引擎管理器切换功能
- ✅ 配置验证机制
- ✅ 回退机制
- ✅ 资源清理
- ✅ Web API接口
- ✅ 引擎特定功能

## 📋 开发指南

### 添加新引擎

1. **创建引擎实现**:
   ```python
   # src/tts_demo/audio/new_engine_generator.py
   class NewEngineGenerator(AudioGenerator):
       async def generate_audio(self, text, voice, rate, volume):
           # 实现音频生成逻辑
           pass
   ```

2. **注册引擎**:
   ```python
   # 在TTSEngineType中添加新类型
   NEW_ENGINE = "new_engine"
   
   # 在TTSEngineFactory中注册
   self.register_engine(
       TTSEngineType.NEW_ENGINE,
       NewEngineGenerator,
       validator=self._validate_new_engine_config
   )
   ```

3. **更新配置模型**:
   ```python
   # 在TTSConfig中添加引擎特定配置
   new_engine_config: Dict[str, Any] = field(default_factory=dict)
   ```

### 最佳实践

1. **错误处理**: 实现完善的异常处理和日志记录
2. **资源管理**: 正确实现cleanup方法
3. **配置验证**: 添加配置验证逻辑
4. **文档更新**: 更新相关文档和示例
5. **测试覆盖**: 添加相应的测试用例

## 🚨 注意事项

1. **依赖安装**: 某些引擎需要额外的依赖包
2. **模型下载**: 本地引擎需要下载相应的模型文件
3. **API配置**: 云端引擎需要有效的API密钥
4. **性能考虑**: 不同引擎的性能和延迟差异较大
5. **兼容性**: 某些引擎可能对系统环境有特殊要求

## 🔮 未来计划

- [ ] 添加更多TTS引擎支持
- [ ] 实现引擎性能监控
- [ ] 添加语音质量评估
- [ ] 支持自定义引擎插件
- [ ] 优化引擎切换性能
- [ ] 添加批量处理支持
