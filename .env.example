# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
DEFAULT_LLM_MODEL=gpt-4o-mini

# Langflow配置
LANGFLOW_BASE_URL=http://10.11.16.24:37860
LANGFLOW_API_KEY=
LANGFLOW_FLOW_ID=your_flow_id_here

# TTS配置
DEFAULT_TTS_VOICE=zh-CN-YunyangNeural
DEFAULT_TTS_RATE=+25%
DEFAULT_TTS_VOLUME=+0%

# 使用说明：
# 1. 复制此文件为 .env
# 2. 填入你的实际配置值
# 3. 如果使用OpenAI，确保设置 OPENAI_API_KEY
# 4. 如果使用Langflow，确保设置 LANGFLOW_FLOW_ID，API_KEY可选
# 5. 运行时可以通过命令行参数覆盖这些环境变量
