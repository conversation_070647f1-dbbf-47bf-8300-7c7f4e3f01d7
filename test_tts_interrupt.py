#!/usr/bin/env python3
"""
TTS中断功能测试脚本

测试新添加的TTS中断功能：当新消息开始处理时，自动中断上一次的TTS朗读
"""

import asyncio
import aiohttp
import json
import sys
import time
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

async def test_tts_interrupt():
    """测试TTS中断功能"""
    session_id = "test-interrupt-session"
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            print("=== TTS中断功能测试 ===")
            print(f"会话ID: {session_id}")
            print(f"服务器: {base_url}")
            print()
            
            # 1. 发送第一个消息（较长的文本，确保TTS有足够时间播放）
            first_message = {
                "content": "请详细介绍一下人工智能的发展历史，包括从早期的图灵测试到现代的深度学习技术，以及未来可能的发展方向。这是一个很长的问题，需要详细的回答。"
            }
            
            print("1. 发送第一个消息（长文本）...")
            async with session.post(
                f"{base_url}/api/sessions/{session_id}/send",
                json=first_message,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    print("✓ 第一个消息发送成功")
                else:
                    print(f"✗ 第一个消息发送失败: {response.status}")
                    return
            
            # 2. 等待一段时间让TTS开始播放
            print("2. 等待3秒让TTS开始播放...")
            await asyncio.sleep(3)
            
            # 3. 检查TTS状态
            print("3. 检查TTS状态...")
            async with session.get(f"{base_url}/api/sessions/{session_id}/tts/status") as response:
                if response.status == 200:
                    status = await response.json()
                    print(f"   TTS状态: {status}")
                    if status.get('is_playing'):
                        print("✓ TTS正在播放")
                    else:
                        print("⚠ TTS未在播放")
                else:
                    print(f"✗ 获取TTS状态失败: {response.status}")
            
            # 4. 立即发送第二个消息（应该中断第一个TTS）
            second_message = {
                "content": "这是第二个消息，应该会中断第一个消息的TTS播放。"
            }
            
            print("4. 发送第二个消息（应该中断第一个TTS）...")
            async with session.post(
                f"{base_url}/api/sessions/{session_id}/send",
                json=second_message,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    print("✓ 第二个消息发送成功")
                    print("   如果功能正常，第一个TTS应该被中断，第二个TTS应该开始播放")
                else:
                    print(f"✗ 第二个消息发送失败: {response.status}")
                    return
            
            # 5. 等待一段时间观察结果
            print("5. 等待5秒观察TTS中断效果...")
            await asyncio.sleep(5)
            
            # 6. 再次检查TTS状态
            print("6. 检查最终TTS状态...")
            async with session.get(f"{base_url}/api/sessions/{session_id}/tts/status") as response:
                if response.status == 200:
                    status = await response.json()
                    print(f"   最终TTS状态: {status}")
                else:
                    print(f"✗ 获取最终TTS状态失败: {response.status}")
            
            print("\n=== 测试完成 ===")
            print("请观察以下现象来验证功能是否正常：")
            print("1. 第一个消息开始TTS播放")
            print("2. 第二个消息发送后，第一个TTS立即停止")
            print("3. 第二个消息的TTS立即开始播放")
            print("4. 没有两个TTS同时播放的情况")
            
        except Exception as e:
            print(f"测试过程中出错: {e}")

async def test_multiple_interrupts():
    """测试多次连续中断"""
    session_id = "test-multiple-interrupts"
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            print("\n=== 多次连续中断测试 ===")
            print(f"会话ID: {session_id}")
            print()
            
            messages = [
                "第一个消息：这是一个较长的文本，用于测试TTS播放。",
                "第二个消息：立即中断上一个。",
                "第三个消息：再次中断。",
                "第四个消息：最后一次中断测试。"
            ]
            
            for i, message_content in enumerate(messages, 1):
                print(f"{i}. 发送消息: {message_content[:20]}...")
                
                message = {"content": message_content}
                async with session.post(
                    f"{base_url}/api/sessions/{session_id}/send",
                    json=message,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        print(f"✓ 消息 {i} 发送成功")
                    else:
                        print(f"✗ 消息 {i} 发送失败: {response.status}")
                
                # 短暂等待，模拟用户快速发送消息的场景
                await asyncio.sleep(1)
            
            print("\n多次连续中断测试完成")
            print("应该只听到最后一个消息的TTS播放")
            
        except Exception as e:
            print(f"多次中断测试过程中出错: {e}")

async def test_websocket_interrupt():
    """测试WebSocket模式下的TTS中断"""
    session_id = "test-ws-interrupt"
    base_url = "ws://localhost:8000"
    
    try:
        print("\n=== WebSocket模式TTS中断测试 ===")
        print(f"会话ID: {session_id}")
        print()
        
        import websockets
        
        uri = f"{base_url}/ws/{session_id}"
        async with websockets.connect(uri) as websocket:
            print("✓ WebSocket连接建立")
            
            # 发送第一个消息
            first_message = {
                "type": "chat",
                "content": "这是WebSocket模式下的第一个长消息，用于测试TTS中断功能。请详细说明一下相关的技术原理。"
            }
            
            await websocket.send(json.dumps(first_message))
            print("✓ 第一个消息已发送")
            
            # 等待一段时间
            await asyncio.sleep(2)
            
            # 发送第二个消息
            second_message = {
                "type": "chat",
                "content": "这是第二个消息，应该中断第一个消息的TTS。"
            }
            
            await websocket.send(json.dumps(second_message))
            print("✓ 第二个消息已发送（应该中断第一个TTS）")
            
            # 接收一些响应消息
            print("接收服务器响应...")
            for _ in range(5):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    data = json.loads(response)
                    print(f"收到: {data.get('type', 'unknown')} - {str(data)[:100]}...")
                except asyncio.TimeoutError:
                    break
                except Exception as e:
                    print(f"接收消息错误: {e}")
                    break
            
            print("WebSocket模式测试完成")
            
    except ImportError:
        print("⚠ websockets库未安装，跳过WebSocket测试")
        print("可以运行: pip install websockets")
    except Exception as e:
        print(f"WebSocket测试过程中出错: {e}")

async def main():
    """主测试函数"""
    print("TTS中断功能测试")
    print("================")
    print("注意: 请确保Web服务器正在运行 (uv run scripts/start_web.py)")
    print("并且TTS功能已启用")
    print()
    
    # 基本中断测试
    await test_tts_interrupt()
    
    # 等待一段时间
    await asyncio.sleep(2)
    
    # 多次连续中断测试
    await test_multiple_interrupts()
    
    # 等待一段时间
    await asyncio.sleep(2)
    
    # WebSocket模式测试
    await test_websocket_interrupt()
    
    print("\n所有测试完成！")
    print("请检查音频播放是否符合预期：")
    print("- 新消息应该立即中断上一次的TTS播放")
    print("- 不应该有多个TTS同时播放")
    print("- 中断应该是即时的，没有明显延迟")

if __name__ == "__main__":
    asyncio.run(main())
