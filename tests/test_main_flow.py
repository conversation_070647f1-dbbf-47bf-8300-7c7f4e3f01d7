#!/usr/bin/env python3
"""
TTS Demo 主流程测试

测试项目的核心功能和主要流程，确保基本功能正常工作
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

import asyncio
from unittest.mock import Mock, patch


def test_project_structure():
    """测试项目基本结构"""
    try:
        base_path = Path(__file__).parent.parent
        
        # 检查核心目录
        required_dirs = [
            "src/tts_demo",
            "src/tts_demo/config",
            "src/tts_demo/llm", 
            "src/tts_demo/speaker",
            "src/tts_demo/web",
            "scripts",
            "tests",
            "config"
        ]
        
        for dir_path in required_dirs:
            full_path = base_path / dir_path
            assert full_path.exists(), f"目录不存在: {dir_path}"
        
        # 检查核心文件
        required_files = [
            "src/tts_demo/__init__.py",
            "src/tts_demo/main.py",
            "scripts/run_demo.py",
            "pyproject.toml",
            "README.md"
        ]
        
        for file_path in required_files:
            full_path = base_path / file_path
            assert full_path.exists(), f"文件不存在: {file_path}"
        
        print("✓ 项目结构正常")
        return True
    except Exception as e:
        print(f"✗ 项目结构测试失败: {e}")
        return False


def test_core_imports():
    """测试核心模块导入"""
    try:
        # 测试配置模块
        from tts_demo.config import get_settings, get_app_config
        
        # 测试LLM模块
        from tts_demo.llm import OpenAIClient, LangflowClient
        
        # 测试Speaker模块
        from tts_demo.speaker import StreamSpeaker, PlaybackState
        
        # 测试Web模块
        from tts_demo.web import create_app
        
        # 测试主模块
        from tts_demo.main import run_llm_tts_demo
        
        print("✓ 核心模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False


def test_config_system():
    """测试配置系统基本功能"""
    try:
        from tts_demo.config import get_settings, get_app_config
        
        # 测试设置获取
        settings = get_settings()
        assert settings is not None
        
        # 测试应用配置获取
        config = get_app_config()
        assert config is not None
        assert hasattr(config, 'llm')
        assert hasattr(config, 'tts')
        assert hasattr(config, 'web')
        
        print("✓ 配置系统正常")
        return True
    except Exception as e:
        print(f"✗ 配置系统测试失败: {e}")
        return False


def test_llm_client_creation():
    """测试LLM客户端创建"""
    try:
        from tts_demo.llm import OpenAIClient, LangflowClient
        
        # 测试OpenAI客户端创建
        openai_client = OpenAIClient(api_key="test", model="test")
        assert openai_client is not None
        assert hasattr(openai_client, 'chat_completion')
        assert hasattr(openai_client, 'extract_content')
        
        # 测试Langflow客户端创建
        langflow_client = LangflowClient(
            base_url="http://test",
            flow_id="test"
        )
        assert langflow_client is not None
        assert hasattr(langflow_client, 'chat_completion')
        assert hasattr(langflow_client, 'extract_content')
        
        print("✓ LLM客户端创建正常")
        return True
    except Exception as e:
        print(f"✗ LLM客户端测试失败: {e}")
        return False


def test_tts_component_creation():
    """测试TTS组件创建"""
    try:
        from tts_demo.speaker import StreamSpeaker, PlaybackState
        
        # 测试StreamSpeaker创建
        speaker = StreamSpeaker(
            voice="zh-CN-YunyangNeural",
            rate="+25%",
            volume="+0%"
        )
        assert speaker is not None
        assert hasattr(speaker, 'start')
        assert hasattr(speaker, 'stop')
        assert hasattr(speaker, 'process_stream')
        
        # 测试PlaybackState枚举
        assert hasattr(PlaybackState, 'STOPPED')
        assert hasattr(PlaybackState, 'PLAYING')
        assert hasattr(PlaybackState, 'PAUSED')
        
        print("✓ TTS组件创建正常")
        return True
    except Exception as e:
        print(f"✗ TTS组件测试失败: {e}")
        return False


def test_web_app_creation():
    """测试Web应用创建"""
    try:
        from tts_demo.web import create_app
        from tts_demo.config import get_app_config
        
        config = get_app_config()
        app = create_app(config.web)
        
        assert app is not None
        assert hasattr(app, 'routes')
        
        print("✓ Web应用创建正常")
        return True
    except Exception as e:
        print(f"✗ Web应用创建测试失败: {e}")
        return False


def test_main_entry_points():
    """测试主要入口点"""
    try:
        # 测试主程序入口
        from tts_demo.main import run_llm_tts_demo, create_argument_parser
        
        # 测试参数解析器创建
        parser = create_argument_parser()
        assert parser is not None
        
        # 测试主函数存在
        assert callable(run_llm_tts_demo)
        
        print("✓ 主要入口点正常")
        return True
    except Exception as e:
        print(f"✗ 入口点测试失败: {e}")
        return False


def run_main_flow_tests():
    """运行主流程测试"""
    print("TTS Demo 主流程测试")
    print("=" * 50)
    
    tests = [
        ("项目结构", test_project_structure),
        ("核心模块导入", test_core_imports),
        ("配置系统", test_config_system),
        ("LLM客户端", test_llm_client_creation),
        ("TTS组件", test_tts_component_creation),
        ("Web应用", test_web_app_creation),
        ("入口点", test_main_entry_points),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 30)
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有主流程测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查问题")
        return False


if __name__ == "__main__":
    success = run_main_flow_tests()
    sys.exit(0 if success else 1)
