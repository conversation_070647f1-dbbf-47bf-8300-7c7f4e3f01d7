<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置面板优化演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .demo-container {
            height: 100vh;
            display: flex;
            position: relative;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        /* 配置面板样式 */
        .config-sidebar {
            width: 320px;
            height: 100%;
            background-color: #f8f9fa;
            border-left: 1px solid #dee2e6;
            flex-shrink: 0;
            transition: margin-right 0.3s ease-in-out;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .config-sidebar.collapsed {
            margin-right: -320px;
        }

        .config-header {
            flex-shrink: 0;
            padding: 1rem;
            background-color: #ffffff;
            border-bottom: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            z-index: 10;
        }

        .config-content {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 1rem;
            min-height: 0;
        }

        .config-footer {
            flex-shrink: 0;
            padding: 1rem;
            background-color: #ffffff;
            border-top: 1px solid #dee2e6;
            box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
            z-index: 10;
        }

        .config-content::-webkit-scrollbar {
            width: 6px;
        }

        .config-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .config-content::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }

        .config-content::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }

        .config-group {
            margin-bottom: 1.5rem;
        }

        .config-group:last-child {
            margin-bottom: 0;
        }

        .config-group-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .config-group-title i {
            color: #007bff;
        }

        .config-section {
            margin-bottom: 1.25rem;
            padding: 1rem;
            background-color: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: box-shadow 0.2s ease;
            animation: slideInRight 0.3s ease-out;
        }

        .config-section:hover {
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        }

        .form-label-sm {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.4rem;
            color: #495057;
        }

        .form-control-sm, .form-select-sm {
            font-size: 0.875rem;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
        }

        .form-control-sm:focus, .form-select-sm:focus {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .demo-info {
            text-align: center;
            max-width: 600px;
        }

        .feature-list {
            text-align: left;
            margin-top: 2rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .feature-item i {
            color: #28a745;
            margin-right: 0.5rem;
        }

        @media (max-width: 768px) {
            .config-sidebar {
                width: 100%;
                height: auto;
                max-height: 300px;
                border-left: none;
                border-top: 1px solid #dee2e6;
                margin-right: 0;
            }

            .config-sidebar.collapsed {
                margin-right: 0;
                height: 0;
                overflow: hidden;
            }

            .config-content {
                max-height: 180px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="demo-info">
                <h1 class="display-4 mb-4">配置面板优化演示</h1>
                <p class="lead">体验全新设计的配置面板，具有固定头部和底部，可滚动的内容区域。</p>
                
                <button class="btn btn-primary btn-lg mb-4" onclick="toggleConfig()">
                    <i class="bi bi-gear"></i>
                    打开配置面板
                </button>

                <div class="feature-list">
                    <h5>主要优化特性：</h5>
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill"></i>
                        <span>固定头部和底部，重要操作始终可见</span>
                    </div>
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill"></i>
                        <span>独立滚动的配置内容区域</span>
                    </div>
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill"></i>
                        <span>配置项智能分组，层次清晰</span>
                    </div>
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill"></i>
                        <span>响应式设计，适配各种设备</span>
                    </div>
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill"></i>
                        <span>流畅的动画和交互反馈</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置面板 -->
        <div class="config-sidebar collapsed" id="configSidebar">
            <!-- 配置面板头部 - 固定 -->
            <div class="config-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-gear"></i>
                        配置设置
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleConfig()">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>

            <!-- 配置面板内容 - 可滚动 -->
            <div class="config-content">
                <!-- LLM配置组 -->
                <div class="config-group">
                    <div class="config-group-title">
                        <i class="bi bi-cpu"></i>
                        LLM配置
                    </div>

                    <div class="config-section">
                        <div class="mb-3">
                            <label class="form-label form-label-sm">API密钥</label>
                            <input type="password" class="form-control form-control-sm" placeholder="输入API密钥">
                        </div>
                        <div class="mb-3">
                            <label class="form-label form-label-sm">模型</label>
                            <select class="form-select form-select-sm">
                                <option>gpt-3.5-turbo</option>
                                <option>gpt-4</option>
                                <option>gpt-4-turbo</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- TTS配置组 -->
                <div class="config-group">
                    <div class="config-group-title">
                        <i class="bi bi-volume-up"></i>
                        TTS配置
                    </div>

                    <div class="config-section">
                        <div class="mb-3">
                            <label class="form-label form-label-sm">语音</label>
                            <select class="form-select form-select-sm">
                                <option>alloy</option>
                                <option>echo</option>
                                <option>fable</option>
                                <option>onyx</option>
                                <option>nova</option>
                                <option>shimmer</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label form-label-sm">语速</label>
                            <input type="range" class="form-range" min="0.25" max="4.0" step="0.25" value="1.0">
                        </div>
                        <div class="mb-3">
                            <label class="form-label form-label-sm">音量</label>
                            <input type="range" class="form-range" min="0" max="100" value="80">
                        </div>
                    </div>
                </div>

                <!-- 高级配置组 -->
                <div class="config-group">
                    <div class="config-group-title">
                        <i class="bi bi-gear-fill"></i>
                        高级配置
                    </div>

                    <div class="config-section">
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="enableCache" checked>
                            <label class="form-check-label" for="enableCache">
                                启用缓存
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="debugMode">
                            <label class="form-check-label" for="debugMode">
                                调试模式
                            </label>
                        </div>
                        <div class="mb-3">
                            <label class="form-label form-label-sm">通信方式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="commMode" id="websocket" checked>
                                <label class="form-check-label" for="websocket">
                                    WebSocket
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="commMode" id="sse">
                                <label class="form-check-label" for="sse">
                                    Server-Sent Events
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置面板底部 - 固定 -->
            <div class="config-footer">
                <button class="btn btn-primary btn-sm w-100 mb-2" onclick="saveConfig()">
                    <i class="bi bi-check-circle"></i>
                    保存配置
                </button>
                <button class="btn btn-outline-secondary btn-sm w-100" onclick="testConfig()">
                    <i class="bi bi-volume-up"></i>
                    测试语音
                </button>
            </div>
        </div>
    </div>

    <script>
        function toggleConfig() {
            const sidebar = document.getElementById('configSidebar');
            sidebar.classList.toggle('collapsed');
        }

        function saveConfig() {
            // 模拟保存配置
            alert('配置已保存！');
        }

        function testConfig() {
            // 模拟测试语音
            alert('正在测试语音...');
        }

        // 演示自动打开配置面板
        setTimeout(() => {
            const sidebar = document.getElementById('configSidebar');
            if (sidebar.classList.contains('collapsed')) {
                toggleConfig();
            }
        }, 2000);
    </script>
</body>
</html>
