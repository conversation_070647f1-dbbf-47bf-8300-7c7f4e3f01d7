"""
事件系统

定义TTS系统中的事件类型和事件总线实现。
"""

import time
import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Dict, List, Callable, Optional
from collections import defaultdict

# 避免循环导入，使用TYPE_CHECKING
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .models import TextChunk, AudioData, PlaybackState, ProcessingStats

logger = logging.getLogger(__name__)


@dataclass
class Event(ABC):
    """事件基类"""
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

    @property
    @abstractmethod
    def event_type(self) -> str:
        """事件类型"""
        pass


@dataclass
class TextChunkEvent(Event):
    """文本块事件"""
    chunk: 'TextChunk' = None

    @property
    def event_type(self) -> str:
        return "text_chunk"


@dataclass
class AudioGeneratedEvent(Event):
    """音频生成事件"""
    audio_data: 'AudioData' = None
    text: str = ""

    @property
    def event_type(self) -> str:
        return "audio_generated"


@dataclass
class PlaybackStateEvent(Event):
    """播放状态事件"""
    old_state: 'PlaybackState' = None
    new_state: 'PlaybackState' = None

    @property
    def event_type(self) -> str:
        return "playback_state"


@dataclass
class StatsUpdateEvent(Event):
    """统计更新事件"""
    stats: 'ProcessingStats' = None

    @property
    def event_type(self) -> str:
        return "stats_update"


@dataclass
class ErrorEvent(Event):
    """错误事件"""
    error: Exception = None
    context: str = ""

    @property
    def event_type(self) -> str:
        return "error"


@dataclass
class CacheEvent(Event):
    """缓存事件"""
    action: str = ""  # "hit", "miss", "put", "clear"
    key: Optional[str] = None
    size: Optional[int] = None

    @property
    def event_type(self) -> str:
        return "cache"


@dataclass
class QueueEvent(Event):
    """队列事件"""
    action: str = ""  # "put", "get", "full", "empty"
    queue_name: str = ""
    size: int = 0

    @property
    def event_type(self) -> str:
        return "queue"


class SimpleEventBus:
    """简单事件总线实现"""

    def __init__(self):
        self._handlers: Dict[str, List[Callable[[Event], Any]]] = defaultdict(list)
        self._async_handlers: Dict[str, List[Callable[[Event], Any]]] = defaultdict(list)
        self._running = False
        self._event_queue = asyncio.Queue()
        self._worker_task: Optional[asyncio.Task] = None

    def subscribe(self, event_type: str, handler: Callable[[Event], Any], async_handler: bool = False):
        """订阅事件

        Args:
            event_type: 事件类型
            handler: 事件处理函数
            async_handler: 是否为异步处理函数
        """
        if async_handler:
            self._async_handlers[event_type].append(handler)
        else:
            self._handlers[event_type].append(handler)

        logger.debug(f"订阅事件: {event_type}, 处理器数量: {len(self._handlers[event_type]) + len(self._async_handlers[event_type])}")

    def unsubscribe(self, event_type: str, handler: Callable[[Event], Any]):
        """取消订阅事件"""
        if handler in self._handlers[event_type]:
            self._handlers[event_type].remove(handler)
        if handler in self._async_handlers[event_type]:
            self._async_handlers[event_type].remove(handler)

        logger.debug(f"取消订阅事件: {event_type}")

    async def emit(self, event: Event):
        """发送事件"""
        if not self._running:
            await self.start()

        await self._event_queue.put(event)

    def emit_sync(self, event: Event):
        """同步发送事件（仅处理同步处理器）"""
        event_type = event.event_type

        # 处理同步处理器
        for handler in self._handlers[event_type]:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"事件处理器错误 ({event_type}): {e}")

    async def start(self):
        """启动事件总线"""
        if self._running:
            return

        self._running = True
        self._worker_task = asyncio.create_task(self._event_worker())
        logger.info("事件总线已启动")

    async def stop(self):
        """停止事件总线"""
        if not self._running:
            return

        self._running = False

        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass

        logger.info("事件总线已停止")

    async def _event_worker(self):
        """事件处理工作循环"""
        while self._running:
            try:
                # 等待事件，设置超时避免无限等待
                event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)
                await self._process_event(event)
                self._event_queue.task_done()
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"事件处理工作循环错误: {e}")

    async def _process_event(self, event: Event):
        """处理单个事件"""
        event_type = event.event_type

        # 处理同步处理器
        for handler in self._handlers[event_type]:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"同步事件处理器错误 ({event_type}): {e}")

        # 处理异步处理器
        for handler in self._async_handlers[event_type]:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)
            except Exception as e:
                logger.error(f"异步事件处理器错误 ({event_type}): {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取事件总线统计信息"""
        total_handlers = sum(len(handlers) for handlers in self._handlers.values())
        total_async_handlers = sum(len(handlers) for handlers in self._async_handlers.values())

        return {
            "running": self._running,
            "total_handlers": total_handlers,
            "total_async_handlers": total_async_handlers,
            "queue_size": self._event_queue.qsize(),
            "event_types": list(set(self._handlers.keys()) | set(self._async_handlers.keys()))
        }
