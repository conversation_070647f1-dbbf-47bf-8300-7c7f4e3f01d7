"""
TTS Demo 核心模块

现代化的TTS系统核心组件：
- 清晰的接口定义
- 类型安全的数据模型
- 事件驱动架构
- 统一的异常处理
"""

from .interfaces import (
    TextProcessor,
    AudioGenerator,
    AudioPlayer,
    CacheManager,
    QueueManager,
    EventBus,
    TTSEngine
)

from .exceptions import (
    TTSError,
    TextProcessingError,
    AudioGenerationError,
    AudioPlaybackError,
    CacheError,
    QueueError
)

from .events import (
    Event,
    TextChunkEvent,
    AudioGeneratedEvent,
    PlaybackStateEvent,
    StatsUpdateEvent,
    ErrorEvent,
    SimpleEventBus
)

from .models import (
    TextChunk,
    AudioData,
    PlaybackState,
    ProcessingStats,
    TTSConfig
)

__all__ = [
    # 接口
    "TextProcessor",
    "AudioGenerator",
    "AudioPlayer",
    "CacheManager",
    "QueueManager",
    "EventBus",
    "TTSEngine",

    # 异常
    "TTSError",
    "TextProcessingError",
    "AudioGenerationError",
    "AudioPlaybackError",
    "CacheError",
    "QueueError",

    # 事件
    "Event",
    "TextChunkEvent",
    "AudioGeneratedEvent",
    "PlaybackStateEvent",
    "StatsUpdateEvent",
    "ErrorEvent",
    "SimpleEventBus",

    # 模型
    "TextChunk",
    "AudioData",
    "PlaybackState",
    "ProcessingStats",
    "TTSConfig"
]
