"""
TTS系统异常定义

定义TTS系统中使用的各种异常类型，提供清晰的错误分类和处理。
"""

from typing import Optional, Any, Dict


class TTSError(Exception):
    """TTS系统基础异常"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class TextProcessingError(TTSError):
    """文本处理异常"""
    
    def __init__(self, message: str, text: Optional[str] = None, 
                 error_code: Optional[str] = None, context: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, context)
        self.text = text


class AudioGenerationError(TTSError):
    """音频生成异常"""
    
    def __init__(self, message: str, text: Optional[str] = None, 
                 voice: Optional[str] = None, error_code: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, context)
        self.text = text
        self.voice = voice


class AudioPlaybackError(TTSError):
    """音频播放异常"""
    
    def __init__(self, message: str, audio_info: Optional[str] = None,
                 error_code: Optional[str] = None, context: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, context)
        self.audio_info = audio_info


class CacheError(TTSError):
    """缓存异常"""
    
    def __init__(self, message: str, cache_key: Optional[str] = None,
                 operation: Optional[str] = None, error_code: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, context)
        self.cache_key = cache_key
        self.operation = operation


class QueueError(TTSError):
    """队列异常"""
    
    def __init__(self, message: str, queue_name: Optional[str] = None,
                 operation: Optional[str] = None, error_code: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, context)
        self.queue_name = queue_name
        self.operation = operation


class ConfigurationError(TTSError):
    """配置异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None,
                 config_value: Optional[Any] = None, error_code: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, context)
        self.config_key = config_key
        self.config_value = config_value


class NetworkError(TTSError):
    """网络异常"""
    
    def __init__(self, message: str, url: Optional[str] = None,
                 status_code: Optional[int] = None, error_code: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, context)
        self.url = url
        self.status_code = status_code


class TimeoutError(TTSError):
    """超时异常"""
    
    def __init__(self, message: str, timeout_seconds: Optional[float] = None,
                 operation: Optional[str] = None, error_code: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, context)
        self.timeout_seconds = timeout_seconds
        self.operation = operation


class ResourceError(TTSError):
    """资源异常"""
    
    def __init__(self, message: str, resource_type: Optional[str] = None,
                 resource_id: Optional[str] = None, error_code: Optional[str] = None,
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, context)
        self.resource_type = resource_type
        self.resource_id = resource_id


# 错误代码常量
class ErrorCodes:
    """错误代码常量"""
    
    # 文本处理错误
    TEXT_FILTER_FAILED = "TEXT_001"
    TEXT_SEGMENTATION_FAILED = "TEXT_002"
    TEXT_BREAK_POSITION_FAILED = "TEXT_003"
    
    # 音频生成错误
    AUDIO_GENERATION_FAILED = "AUDIO_001"
    AUDIO_ENGINE_UNAVAILABLE = "AUDIO_002"
    AUDIO_FORMAT_UNSUPPORTED = "AUDIO_003"
    
    # 音频播放错误
    AUDIO_PLAYBACK_FAILED = "PLAYBACK_001"
    AUDIO_DEVICE_UNAVAILABLE = "PLAYBACK_002"
    AUDIO_FORMAT_INCOMPATIBLE = "PLAYBACK_003"
    
    # 缓存错误
    CACHE_PUT_FAILED = "CACHE_001"
    CACHE_GET_FAILED = "CACHE_002"
    CACHE_MEMORY_EXCEEDED = "CACHE_003"
    CACHE_CORRUPTION = "CACHE_004"
    
    # 队列错误
    QUEUE_FULL = "QUEUE_001"
    QUEUE_EMPTY = "QUEUE_002"
    QUEUE_TIMEOUT = "QUEUE_003"
    
    # 配置错误
    CONFIG_INVALID = "CONFIG_001"
    CONFIG_MISSING = "CONFIG_002"
    CONFIG_TYPE_ERROR = "CONFIG_003"
    
    # 网络错误
    NETWORK_CONNECTION_FAILED = "NETWORK_001"
    NETWORK_TIMEOUT = "NETWORK_002"
    NETWORK_INVALID_RESPONSE = "NETWORK_003"
    
    # 资源错误
    RESOURCE_NOT_FOUND = "RESOURCE_001"
    RESOURCE_ACCESS_DENIED = "RESOURCE_002"
    RESOURCE_EXHAUSTED = "RESOURCE_003"


def create_error_from_exception(exc: Exception, context: Optional[Dict[str, Any]] = None) -> TTSError:
    """从标准异常创建TTS异常"""
    if isinstance(exc, TTSError):
        return exc
    
    error_message = str(exc)
    error_type = type(exc).__name__
    
    # 根据异常类型选择合适的TTS异常
    if isinstance(exc, (ConnectionError, OSError)):
        return NetworkError(
            f"网络错误: {error_message}",
            error_code=ErrorCodes.NETWORK_CONNECTION_FAILED,
            context=context
        )
    elif isinstance(exc, TimeoutError):
        return TimeoutError(
            f"操作超时: {error_message}",
            error_code=ErrorCodes.NETWORK_TIMEOUT,
            context=context
        )
    elif isinstance(exc, (ValueError, TypeError)):
        return ConfigurationError(
            f"配置错误: {error_message}",
            error_code=ErrorCodes.CONFIG_INVALID,
            context=context
        )
    elif isinstance(exc, FileNotFoundError):
        return ResourceError(
            f"资源未找到: {error_message}",
            error_code=ErrorCodes.RESOURCE_NOT_FOUND,
            context=context
        )
    elif isinstance(exc, PermissionError):
        return ResourceError(
            f"资源访问被拒绝: {error_message}",
            error_code=ErrorCodes.RESOURCE_ACCESS_DENIED,
            context=context
        )
    else:
        return TTSError(
            f"未知错误 ({error_type}): {error_message}",
            context=context
        )
