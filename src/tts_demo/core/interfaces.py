"""
核心接口定义

定义TTS系统的核心接口，实现依赖倒置和接口隔离原则。
"""

from abc import ABC, abstractmethod
from typing import AsyncGenerator, Optional, Dict, Any, List, Callable
import asyncio
import io

# 避免循环导入，使用字符串类型注解
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .models import TextChunk, AudioData, PlaybackState, ProcessingStats
    from .events import Event


class TextProcessor(ABC):
    """文本处理器接口"""

    @abstractmethod
    def filter_content(self, text: str) -> str:
        """过滤文本内容"""
        pass

    @abstractmethod
    def segment_text(self, text: str, max_words: int) -> int:
        """分词处理"""
        pass

    @abstractmethod
    def find_break_positions(self, text: str, break_chars: List[str]) -> List[int]:
        """查找断句位置"""
        pass

    @abstractmethod
    def clear_cache(self):
        """清理缓存"""
        pass


class AudioGenerator(ABC):
    """音频生成器接口"""

    @abstractmethod
    async def generate_audio(self, text: str, voice: str, rate: str, volume: str) -> Optional['AudioData']:
        """生成音频数据"""
        pass

    @abstractmethod
    async def warmup(self, text: str, voice: str, rate: str, volume: str):
        """预热音频引擎"""
        pass


class AudioPlayer(ABC):
    """音频播放器接口"""

    @abstractmethod
    async def play(self, audio_data: 'AudioData') -> bool:
        """播放音频"""
        pass

    @abstractmethod
    def stop(self):
        """停止播放"""
        pass

    @abstractmethod
    def is_playing(self) -> bool:
        """检查是否正在播放"""
        pass


class CacheManager(ABC):
    """缓存管理器接口"""

    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        pass

    @abstractmethod
    def put(self, key: str, value: Any) -> bool:
        """设置缓存"""
        pass

    @abstractmethod
    def clear(self):
        """清空缓存"""
        pass

    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        pass


class QueueManager(ABC):
    """队列管理器接口"""

    @abstractmethod
    async def put(self, item: Any) -> bool:
        """放入队列"""
        pass

    @abstractmethod
    async def get(self) -> Any:
        """从队列获取"""
        pass

    @abstractmethod
    def qsize(self) -> int:
        """获取队列大小"""
        pass

    @abstractmethod
    def empty(self) -> bool:
        """检查队列是否为空"""
        pass

    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """获取队列统计"""
        pass


class EventBus(ABC):
    """事件总线接口"""

    @abstractmethod
    async def emit(self, event: 'Event'):
        """发送事件"""
        pass

    @abstractmethod
    def subscribe(self, event_type: str, handler: Callable[['Event'], Any]):
        """订阅事件"""
        pass

    @abstractmethod
    def unsubscribe(self, event_type: str, handler: Callable[['Event'], Any]):
        """取消订阅"""
        pass


class TTSEngine(ABC):
    """TTS引擎接口"""

    @abstractmethod
    async def start(self):
        """启动引擎"""
        pass

    @abstractmethod
    async def stop(self):
        """停止引擎"""
        pass

    @abstractmethod
    async def process_stream(self, stream: AsyncGenerator[Any, None],
                           content_extractor: Callable[[Any], Optional[str]]):
        """处理流式输入"""
        pass

    @abstractmethod
    def get_state(self) -> 'PlaybackState':
        """获取当前状态"""
        pass

    @abstractmethod
    def get_stats(self) -> 'ProcessingStats':
        """获取统计信息"""
        pass
