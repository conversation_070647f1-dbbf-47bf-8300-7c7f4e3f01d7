{% extends "base.html" %}

{% block title %}TTS Demo 聊天界面 - 会话{% endblock %}

{% block content %}
<div class="d-flex chat-container">
    <!-- 左侧会话列表 -->
    <div class="sessions-sidebar">
        <div class="p-3 border-bottom">
            <h6 class="mb-0">
                <i class="bi bi-chat-left-text"></i>
                会话列表
            </h6>
        </div>
        <div id="sessionsList">
            <!-- 会话列表将通过JavaScript动态加载 -->
        </div>
        <div class="p-3 border-top">
            <button class="btn btn-outline-primary btn-sm w-100" onclick="createNewSession()">
                <i class="bi bi-plus"></i>
                新建会话
            </button>
        </div>
    </div>

    <!-- 中间聊天区域 -->
    <div class="chat-main">
        <!-- 聊天头部 -->
        <div class="border-bottom p-3 bg-white">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="bi bi-chat-dots"></i>
                        聊天会话
                    </h5>
                    <small class="text-muted">会话ID: {{ session_id }}</small>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <!-- 语音状态和控制 -->
                    <div class="d-flex align-items-center me-3">
                        <small class="text-muted me-2">
                            <i class="bi bi-volume-up me-1"></i>
                            <span id="voiceStatus">就绪</span>
                        </small>
                        <button class="btn btn-outline-success btn-sm me-1" id="startBtn" title="启用语音" onclick="startTTS()" style="display: none;">
                            <i class="bi bi-play-circle"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm me-1" id="stopBtn" title="停止语音" onclick="stopTTS()">
                            <i class="bi bi-stop-circle"></i>
                        </button>
                    </div>
                    <!-- 会话控制按钮 -->
                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="clearChat()">
                        <i class="bi bi-trash"></i>
                        清空聊天
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="toggleConfig()">
                        <i class="bi bi-gear"></i>
                        配置
                    </button>
                </div>
            </div>
        </div>



        <!-- 聊天消息区域 -->
        <div class="flex-grow-1 overflow-auto p-3" id="chatMessages">
            {% for message in messages %}
            <div class="message-item mb-3 {{ 'user-message' if message.role == 'user' else 'assistant-message' }}" data-message-id="{{ message.id }}">
                <div class="d-flex {{ 'justify-content-end' if message.role == 'user' else 'justify-content-start' }}">
                    <div class="message-bubble {{ 'bg-primary text-white' if message.role == 'user' else 'bg-light' }} p-3 rounded">
                        <div class="message-content" data-role="{{ message.role }}">{{ message.content }}</div>
                        <small class="message-time text-muted d-block mt-1">
                            {{ message.timestamp.strftime('%H:%M:%S') }}
                        </small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 输入区域 -->
        <div class="border-top bg-white" style="padding: 1rem; flex-shrink: 0;">
            <div class="input-group">
                <input type="text" class="form-control" id="messageInput" placeholder="输入您的消息..." onkeypress="handleKeyPress(event)" autocomplete="off">
                <button class="btn btn-primary" type="button" onclick="sendMessage()" id="sendButton">
                    <i class="bi bi-send"></i>
                    <span class="d-none d-sm-inline">发送</span>
                </button>
            </div>

            <!-- 状态指示器 -->
            <div id="statusIndicator" class="mt-2" style="display: none;">
                <div class="d-flex align-items-center text-muted">
                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                    <span id="statusText">AI正在思考...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧配置面板 -->
    <div class="config-sidebar collapsed" id="configSidebar">
        <!-- 配置面板头部 - 固定 -->
        <div class="config-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i>
                    配置设置
                </h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="toggleConfig()">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        </div>

        <!-- 配置面板内容 - 可滚动 -->
        <div class="config-content">
            <!-- LLM配置组 -->
            <div class="config-group">
                <div class="config-group-title">
                    <i class="bi bi-cpu"></i>
                    LLM配置
                </div>

                <!-- LLM源选择 -->
                <div class="mb-3">
                    <label class="form-label form-label-sm">LLM源</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="llmSource" id="openai" value="false" checked>
                        <label class="form-check-label" for="openai">
                            OpenAI API
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="llmSource" id="langflow" value="true">
                        <label class="form-check-label" for="langflow">
                            Langflow 工作流
                        </label>
                    </div>
                </div>

            <!-- OpenAI配置 -->
            <div id="openaiConfig" class="config-section">
                <h6 class="text-muted">OpenAI 配置</h6>
                <div class="mb-2">
                    <label class="form-label form-label-sm">模型</label>
                    <input type="text" class="form-control form-control-sm" id="model" value="{{ config.model }}">
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">API Key</label>
                    <input type="password" class="form-control form-control-sm" id="apiKey" placeholder="留空使用环境变量">
                </div>
                <div class="mb-3">
                    <label class="form-label form-label-sm">Base URL</label>
                    <input type="text" class="form-control form-control-sm" id="baseUrl" placeholder="留空使用默认值">
                </div>
            </div>

            <!-- Langflow配置 -->
            <div id="langflowConfig" class="config-section" style="display: none;">
                <h6 class="text-muted">Langflow 配置</h6>
                <div class="mb-2">
                    <label class="form-label form-label-sm">服务器URL</label>
                    <input type="text" class="form-control form-control-sm" id="langflowBaseUrl" value="{{ config.langflow_base_url }}">
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">聊天工作流ID</label>
                    <input type="text" class="form-control form-control-sm" id="langflowChatFlowId" placeholder="必填">
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">操作工作流ID列表</label>
                    <div class="d-flex align-items-center mb-1">
                        <input type="text" class="form-control form-control-sm me-2" id="operatorFlowIdInput" placeholder="输入工作流ID">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addOperatorFlowId()">
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                    <div id="operatorFlowIdsList" class="mt-2">
                        <!-- 动态添加的工作流ID列表 -->
                    </div>
                    <small class="form-text text-muted">点击 + 按钮添加操作工作流ID</small>
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">API Key</label>
                    <input type="password" class="form-control form-control-sm" id="langflowApiKey" placeholder="可选">
                </div>
                <div class="mb-3">
                    <label class="form-label form-label-sm">会话ID</label>
                    <input type="text" class="form-control form-control-sm" id="langflowSessionId" placeholder="可选">
                </div>
            </div>
            </div>

            <!-- TTS配置组 -->
            <div class="config-group">
                <div class="config-group-title">
                    <i class="bi bi-volume-up"></i>
                    TTS配置
                </div>

                <!-- TTS引擎选择 -->
                <div class="mb-3">
                    <label class="form-label form-label-sm">TTS引擎</label>
                    <select class="form-select form-select-sm" id="ttsEngine">
                        <option value="edge_tts">Microsoft Edge TTS (免费)</option>
                        <option value="f5_tts">F5-TTS (开源)</option>
                        <option value="megatts3">MegaTTS3 (需配置)</option>
                        <option value="cosyvoice2">CosyVoice2 (开源)</option>
                        <option value="openai_tts">OpenAI TTS (需API密钥)</option>
                    </select>
                    <small class="form-text text-muted">选择不同的TTS引擎体验不同的语音效果</small>
                </div>

                <!-- 引擎状态显示 -->
                <div class="mb-2">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-success me-2" id="engineStatus">
                            <i class="bi bi-check-circle"></i>
                            已就绪
                        </span>
                        <button class="btn btn-outline-primary btn-sm" onclick="switchTTSEngine()" id="switchEngineBtn">
                            <i class="bi bi-arrow-repeat"></i>
                            切换引擎
                        </button>
                    </div>
                </div>

                <!-- 引擎特定配置区域 -->
                <div id="engineSpecificConfig">
                    <!-- 配置内容将根据选择的引擎动态生成 -->
                </div>

                <div class="mb-2">
                    <label class="form-label form-label-sm">语音</label>
                    <select class="form-select form-select-sm" id="voice">
                        <option value="zh-CN-YunyangNeural">云扬 (男性, 新闻)</option>
                        <option value="zh-CN-XiaoxiaoNeural">晓晓 (女性, 新闻)</option>
                        <option value="zh-CN-XiaoyiNeural">晓伊 (女性, 卡通)</option>
                        <option value="zh-CN-YunxiNeural">云希 (男性, 小说)</option>
                        <option value="zh-CN-YunxiaNeural">云夏 (男性, 卡通)</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">语速</label>
                    <select class="form-select form-select-sm" id="rate">
                        <option value="-50%">很慢</option>
                        <option value="-25%">慢</option>
                        <option value="+0%">正常</option>
                        <option value="+25%" selected>快</option>
                        <option value="+50%">很快</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">音量</label>
                    <select class="form-select form-select-sm" id="volume">
                        <option value="-50%">很小</option>
                        <option value="-25%">小</option>
                        <option value="+0%" selected>正常</option>
                        <option value="+25%">大</option>
                        <option value="+50%">很大</option>
                    </select>
                </div>
            </div>

            <!-- 高级配置组 -->
            <div class="config-group">
                <div class="config-group-title">
                    <i class="bi bi-gear-fill"></i>
                    高级配置
                </div>
                <div class="mb-3">
                    <label class="form-label form-label-sm">首块断句模式</label>
                    <select class="form-select form-select-sm" id="firstChunkBreakMode">
                        <option value="word_segmentation">分词方式</option>
                        <option value="punctuation">标点符号方式</option>
                    </select>
                    <small class="form-text text-muted">分词方式：按词数切分；标点符号方式：优先在标点符号处断句</small>
                </div>
                <div class="mb-2" id="firstChunkSizeContainer">
                    <label class="form-label form-label-sm">首块大小</label>
                    <input type="number" class="form-control form-control-sm" id="firstChunkSize" value="{{ config.first_chunk_size }}" min="1" max="20">
                    <small class="form-text text-muted">仅在分词方式下使用，表示第一个文本块的最大词数</small>
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">最小句子大小</label>
                    <input type="number" class="form-control form-control-sm" id="minSentenceSize" value="{{ config.min_sentence_size }}" min="5" max="100">
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="enableCache" checked>
                    <label class="form-check-label form-label-sm" for="enableCache">
                        启用缓存
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="stream" checked>
                    <label class="form-check-label form-label-sm" for="stream">
                        启用流式响应
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="debug">
                    <label class="form-check-label form-label-sm" for="debug">
                        调试模式
                    </label>
                </div>
                <div class="mb-3">
                    <label class="form-label form-label-sm">通信方式</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="communicationMode" id="websocket" value="websocket" checked>
                        <label class="form-check-label form-label-sm" for="websocket">
                            WebSocket
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="communicationMode" id="sse" value="sse">
                        <label class="form-check-label form-label-sm" for="sse">
                            Server-Sent Events (SSE)
                        </label>
                    </div>
                    <small class="form-text text-muted">WebSocket支持双向通信，SSE仅支持服务器到客户端的单向通信</small>
                </div>
            </div>
        </div>

        <!-- 配置面板底部 - 固定 -->
        <div class="config-footer">
            <!-- 保存配置按钮 -->
            <button class="btn btn-primary btn-sm w-100 mb-2" onclick="saveConfig()">
                <i class="bi bi-check-circle"></i>
                保存配置
            </button>

            <!-- 测试语音按钮 -->
            <button class="btn btn-outline-secondary btn-sm w-100" onclick="testTTSConfig()">
                <i class="bi bi-volume-up"></i>
                测试语音
            </button>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_scripts %}
<script>
// 全局变量
let websocket = null;
let eventSource = null;
let sessionId = '{{ session_id }}';
let isGenerating = false;
let communicationMode = 'websocket'; // 默认使用WebSocket

// 从URL获取会话ID（如果模板变量不可用）
if (!sessionId || sessionId === '') {
    const pathParts = window.location.pathname.split('/');
    if (pathParts[1] === 'chat' && pathParts[2]) {
        sessionId = pathParts[2];
    }
}

// 渲染页面上已有的消息
function renderExistingMessages() {
    const messageContents = document.querySelectorAll('.message-content[data-role]');
    messageContents.forEach(contentDiv => {
        const role = contentDiv.getAttribute('data-role');
        const content = contentDiv.textContent;

        if (role === 'assistant') {
            // 只对助手消息进行markdown渲染
            contentDiv.innerHTML = renderMarkdown(content);
        } else {
            // 用户消息保持原样，但进行HTML转义
            contentDiv.innerHTML = escapeHtml(content);
        }
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadConfig(); // 先加载配置以获取通信方式
    setupEventListeners();
    loadSessions();
    renderExistingMessages(); // 渲染已有消息
    scrollToBottom();

    // 确保输入框可见性
    ensureInputVisibility();

    // 启动TTS状态更新器
    startStatusUpdater();
});

// 确保输入框始终可见
function ensureInputVisibility() {
    const inputArea = document.querySelector('.chat-main .border-top');
    const messageInput = document.getElementById('messageInput');

    if (inputArea && messageInput) {
        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            setTimeout(() => {
                inputArea.scrollIntoView({ behavior: 'smooth', block: 'end' });
            }, 100);
        });

        // 监听输入框焦点
        messageInput.addEventListener('focus', function() {
            setTimeout(() => {
                inputArea.scrollIntoView({ behavior: 'smooth', block: 'end' });
            }, 300); // 等待虚拟键盘弹出
        });
    }
}

// 初始化通信连接
function initializeCommunication() {
    if (communicationMode === 'websocket') {
        initializeWebSocket();
    } else if (communicationMode === 'sse') {
        initializeSSE();
    }
}

// 初始化WebSocket连接
function initializeWebSocket() {
    // 关闭现有的SSE连接
    if (eventSource) {
        eventSource.close();
        eventSource = null;
    }

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/${sessionId}`;

    websocket = new WebSocket(wsUrl);

    websocket.onopen = function(event) {
        console.log('WebSocket连接已建立');
        showToast('WebSocket连接已建立', 'success');
    };

    websocket.onmessage = function(event) {
        const data = JSON.parse(event.data);
        handleMessage(data);
    };

    websocket.onclose = function(event) {
        console.log('WebSocket连接已关闭');
        showToast('WebSocket连接已断开', 'warning');

        // 尝试重连
        setTimeout(() => {
            if (communicationMode === 'websocket' && (!websocket || websocket.readyState === WebSocket.CLOSED)) {
                initializeWebSocket();
            }
        }, 3000);
    };

    websocket.onerror = function(error) {
        console.error('WebSocket错误:', error);
        showToast('WebSocket连接错误', 'error');
    };
}

// 初始化SSE连接
function initializeSSE() {
    // 关闭现有的WebSocket连接
    if (websocket) {
        websocket.close();
        websocket = null;
    }

    const sseUrl = `/sse/${sessionId}`;
    eventSource = new EventSource(sseUrl);

    eventSource.onopen = function(event) {
        console.log('SSE连接已建立');
        showToast('SSE连接已建立', 'success');
    };

    eventSource.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            if (data.type !== 'heartbeat') {
                handleMessage(data);
            }
        } catch (error) {
            console.error('SSE消息解析错误:', error);
        }
    };

    eventSource.onerror = function(error) {
        console.error('SSE错误:', error);
        showToast('SSE连接错误', 'error');

        // 尝试重连
        setTimeout(() => {
            if (communicationMode === 'sse' && eventSource.readyState === EventSource.CLOSED) {
                initializeSSE();
            }
        }, 3000);
    };
}

// 处理消息（WebSocket和SSE通用）
function handleMessage(data) {
    switch (data.type) {
        case 'message':
            addMessageToChat(data.message);
            break;
        case 'start':
            setGeneratingStatus(true);
            break;
        case 'token':
            updateStreamingMessage(data.content, data.message_id);
            break;
        case 'end':
            setGeneratingStatus(false);
            updateMessageContent(data.message);
            break;
        case 'error':
            setGeneratingStatus(false);
            showToast(`错误: ${data.message}`, 'error');
            break;
        case 'heartbeat':
            // SSE心跳消息，忽略
            break;
    }
}

// 根据首块断句模式控制首块大小设置的显示与隐藏
function toggleFirstChunkSizeVisibility() {
    const breakMode = document.getElementById('firstChunkBreakMode').value;
    const firstChunkSizeContainer = document.getElementById('firstChunkSizeContainer');

    if (breakMode === 'word_segmentation') {
        firstChunkSizeContainer.style.display = 'block';
    } else {
        firstChunkSizeContainer.style.display = 'none';
    }
}

// 操作工作流ID管理
let operatorFlowIds = [];

// 添加操作工作流ID
function addOperatorFlowId() {
    const input = document.getElementById('operatorFlowIdInput');
    const flowId = input.value.trim();

    if (!flowId) {
        showToast('请输入工作流ID', 'warning');
        return;
    }

    if (operatorFlowIds.includes(flowId)) {
        showToast('该工作流ID已存在', 'warning');
        return;
    }

    operatorFlowIds.push(flowId);
    input.value = '';
    renderOperatorFlowIdsList();
    saveConfig();
}

// 删除操作工作流ID
function removeOperatorFlowId(flowId) {
    operatorFlowIds = operatorFlowIds.filter(id => id !== flowId);
    renderOperatorFlowIdsList();
    saveConfig();
}

// 渲染操作工作流ID列表
function renderOperatorFlowIdsList() {
    const container = document.getElementById('operatorFlowIdsList');

    if (operatorFlowIds.length === 0) {
        container.innerHTML = '<small class="text-muted">暂无操作工作流ID</small>';
        return;
    }

    container.innerHTML = operatorFlowIds.map(flowId => `
        <div class="d-flex align-items-center justify-content-between mb-1 p-2 bg-light rounded">
            <small class="text-truncate me-2">${escapeHtml(flowId)}</small>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeOperatorFlowId('${escapeHtml(flowId)}')">
                <i class="bi bi-x"></i>
            </button>
        </div>
    `).join('');
}

// 设置事件监听器
function setupEventListeners() {
    // LLM源切换
    document.querySelectorAll('input[name="llmSource"]').forEach(radio => {
        radio.addEventListener('change', function() {
            toggleConfigSections();
        });
    });

    // 首块断句模式变化监听
    document.getElementById('firstChunkBreakMode').addEventListener('change', toggleFirstChunkSizeVisibility);

    // 操作工作流ID输入框回车事件
    document.getElementById('operatorFlowIdInput').addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            addOperatorFlowId();
        }
    });

    // 通信方式切换监听
    document.querySelectorAll('input[name="communicationMode"]').forEach(radio => {
        radio.addEventListener('change', function() {
            saveConfig(); // 立即保存并切换通信方式
        });
    });

    // TTS引擎选择变化监听
    document.getElementById('ttsEngine').addEventListener('change', function() {
        const selectedEngine = this.value;
        updateEngineStatus(selectedEngine);
        renderEngineSpecificConfig(selectedEngine);
        // 自动保存配置
        debounce(saveConfig, 1000)();
    });

    // 配置表单变化时自动保存
    const configInputs = document.querySelectorAll('#openaiConfig input, #langflowConfig input, #voice, #rate, #volume, #firstChunkSize, #minSentenceSize, #enableCache, #debug, #stream');
    configInputs.forEach(input => {
        input.addEventListener('change', debounce(saveConfig, 1000));
    });
}

// 切换配置区域显示
function toggleConfigSections() {
    const useLangflow = document.getElementById('langflow').checked;
    document.getElementById('openaiConfig').style.display = useLangflow ? 'none' : 'block';
    document.getElementById('langflowConfig').style.display = useLangflow ? 'block' : 'none';
}

// 发送消息
async function sendMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();

    if (!message || isGenerating) {
        return;
    }

    // 显示TTS中断提示（如果有正在播放的语音）
    const voiceStatus = document.querySelector('.voice-status');
    if (voiceStatus && voiceStatus.textContent.includes('播放中')) {
        showToast('正在中断上一次语音播放...', 'info');
    }

    // 清空输入框
    input.value = '';

    if (communicationMode === 'websocket') {
        // WebSocket模式
        if (!websocket || websocket.readyState !== WebSocket.OPEN) {
            showToast('WebSocket连接未建立，请稍后重试', 'error');
            return;
        }

        websocket.send(JSON.stringify({
            type: 'chat',
            content: message
        }));
    } else if (communicationMode === 'sse') {
        // SSE模式，使用HTTP API发送消息
        try {
            const response = await fetch(`/api/sessions/${sessionId}/send`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: message
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            showToast('发送消息失败: ' + error.message, 'error');
        }
    }
}

// 处理键盘事件
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// 加载配置
async function loadConfig() {
    try {
        const response = await fetch(`/api/sessions/${sessionId}/config`);
        const config = await response.json();

        // 设置LLM源
        document.getElementById(config.use_langflow ? 'langflow' : 'openai').checked = true;
        toggleConfigSections();

        // 设置配置值
        document.getElementById('model').value = config.model || '';
        document.getElementById('langflowBaseUrl').value = config.langflow_base_url || '';
        document.getElementById('langflowChatFlowId').value = config.langflow_chat_flow_id || '';

        // 处理操作工作流ID列表
        operatorFlowIds = config.langflow_operator_flow_ids || [];
        renderOperatorFlowIdsList();

        // 设置全局流式响应开关
        document.getElementById('stream').checked = config.stream !== false;

        document.getElementById('ttsEngine').value = config.tts_engine || 'edge_tts';
        document.getElementById('voice').value = config.voice || 'zh-CN-YunyangNeural';
        document.getElementById('rate').value = config.rate || '+25%';
        document.getElementById('volume').value = config.volume || '+0%';
        document.getElementById('firstChunkSize').value = config.first_chunk_size || 10;
        document.getElementById('minSentenceSize').value = config.min_sentence_size || 20;

        // 更新引擎状态和特定配置
        updateEngineStatus(config.tts_engine || 'edge_tts');
        renderEngineSpecificConfig(config.tts_engine || 'edge_tts', config);

        // 设置首块断句模式
        let breakMode = 'word_segmentation';
        if (config.first_chunk_break_mode) {
            breakMode = config.first_chunk_break_mode;
        }
        document.getElementById('firstChunkBreakMode').value = breakMode;

        // 根据断句模式控制首块大小设置的显示
        toggleFirstChunkSizeVisibility();

        document.getElementById('enableCache').checked = config.enable_cache !== false;
        document.getElementById('debug').checked = config.debug === true;

        // 设置通信方式
        communicationMode = config.communication_mode || 'websocket';
        document.getElementById(communicationMode).checked = true;

        // 初始化通信连接
        initializeCommunication();

    } catch (error) {
        console.error('加载配置失败:', error);
        // 如果加载配置失败，使用默认的WebSocket连接
        initializeCommunication();
    }
}

// 保存配置
async function saveConfig() {
    const config = {
        use_langflow: document.getElementById('langflow').checked,
        model: document.getElementById('model').value,
        api_key: document.getElementById('apiKey').value || null,
        base_url: document.getElementById('baseUrl').value || null,
        langflow_base_url: document.getElementById('langflowBaseUrl').value,
        langflow_api_key: document.getElementById('langflowApiKey').value || null,
        langflow_chat_flow_id: document.getElementById('langflowChatFlowId').value || null,
        langflow_operator_flow_ids: operatorFlowIds.length > 0 ? operatorFlowIds : null,
        langflow_session_id: document.getElementById('langflowSessionId').value || null,
        tts_engine: document.getElementById('ttsEngine').value,
        voice: document.getElementById('voice').value,
        rate: document.getElementById('rate').value,
        volume: document.getElementById('volume').value,
        first_chunk_size: parseInt(document.getElementById('firstChunkSize').value),
        min_sentence_size: parseInt(document.getElementById('minSentenceSize').value),
        first_chunk_break_mode: document.getElementById('firstChunkBreakMode').value,
        enable_cache: document.getElementById('enableCache').checked,
        debug: document.getElementById('debug').checked,
        stream: document.getElementById('stream').checked,
        communication_mode: document.querySelector('input[name="communicationMode"]:checked').value
    };

    try {
        const response = await fetch(`/api/sessions/${sessionId}/config`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });

        if (response.ok) {
            showToast('配置已保存', 'success');
            console.log('配置已保存:', config);

            // 如果通信方式发生变化，重新初始化连接
            const newCommunicationMode = config.communication_mode;
            if (newCommunicationMode !== communicationMode) {
                communicationMode = newCommunicationMode;
                initializeCommunication();
                showToast(`已切换到${communicationMode === 'websocket' ? 'WebSocket' : 'SSE'}模式`, 'info');
            }
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        console.error('保存配置失败:', error);
        showToast('保存配置失败', 'error');
    }
}

// 测试TTS配置
async function testTTSConfig() {
    const testButton = event.target;
    const originalText = testButton.innerHTML;

    // 显示测试状态
    testButton.innerHTML = '<i class="bi bi-hourglass-split"></i> 测试中...';
    testButton.disabled = true;

    try {
        // 先保存当前配置
        await saveConfig();

        // 预设的测试文本
        const testTexts = [
            "这是一个语音配置测试。您现在听到的是当前配置下的语音效果。",
            "Hello, this is a voice configuration test. You are now hearing the voice effect under the current configuration.",
            "语音合成技术可以将文字转换为自然流畅的语音，为用户提供更好的交互体验。",
            "人工智能助手正在为您朗读这段测试文本，请注意语音的清晰度和自然度。"
        ];

        // 随机选择一段测试文本
        const testText = testTexts[Math.floor(Math.random() * testTexts.length)];

        // 直接调用TTS测试API
        const response = await fetch(`/api/sessions/${sessionId}/test-tts`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                text: testText
            })
        });

        if (response.ok) {
            showToast('正在播放测试语音...', 'info');
        } else {
            const errorData = await response.json();
            throw new Error(errorData.detail || '测试请求失败');
        }

    } catch (error) {
        console.error('测试TTS配置失败:', error);
        showToast('测试失败: ' + error.message, 'error');
    } finally {
        // 恢复按钮状态
        setTimeout(() => {
            testButton.innerHTML = originalText;
            testButton.disabled = false;
        }, 3000);
    }
}

// 语音控制功能
async function stopTTS() {
    try {
        const response = await fetch(`/api/sessions/${sessionId}/tts/stop`, {
            method: 'POST'
        });

        if (response.ok) {
            const result = await response.json();
            updateVoiceStatus('已禁用');
            updateVoiceButtons(false);
            showToast(result.message || '语音播放已停止', 'info');
        } else {
            throw new Error('停止失败');
        }
    } catch (error) {
        console.error('停止TTS失败:', error);
        showToast('停止失败', 'error');
    }
}

async function startTTS() {
    try {
        const response = await fetch(`/api/sessions/${sessionId}/tts/start`, {
            method: 'POST'
        });

        if (response.ok) {
            const result = await response.json();
            updateVoiceStatus('已启用');
            updateVoiceButtons(true);
            showToast(result.message || 'TTS功能已启用', 'success');
        } else {
            throw new Error('启用失败');
        }
    } catch (error) {
        console.error('启用TTS失败:', error);
        showToast('启用失败', 'error');
    }
}



function updateVoiceStatus(status) {
    document.getElementById('voiceStatus').textContent = status;
}

function updateVoiceButtons(ttsEnabled) {
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');

    if (ttsEnabled) {
        startBtn.style.display = 'none';
        stopBtn.style.display = 'inline-block';
    } else {
        startBtn.style.display = 'inline-block';
        stopBtn.style.display = 'none';
    }
}

async function getTTSStatus() {
    try {
        const response = await fetch(`/api/sessions/${sessionId}/tts/status`);
        if (response.ok) {
            const result = await response.json();
            return result.data;
        }
    } catch (error) {
        console.error('获取TTS状态失败:', error);
    }
    return null;
}

// 定期更新TTS状态
function startStatusUpdater() {
    setInterval(async () => {
        const status = await getTTSStatus();
        if (status) {
            // 更新按钮状态
            updateVoiceButtons(status.tts_enabled);

            if (!status.tts_enabled) {
                updateVoiceStatus('已禁用');
            } else if (status.is_running) {
                switch (status.playback_state) {
                    case 'playing':
                        updateVoiceStatus('播放中');
                        break;
                    case 'stopped':
                        updateVoiceStatus('已停止');
                        break;
                    default:
                        updateVoiceStatus('就绪');
                }
            } else {
                updateVoiceStatus('就绪');
            }
        }
    }, 2000); // 每2秒更新一次状态
}

// 添加消息到聊天区域
function addMessageToChat(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-item mb-3 ${message.role === 'user' ? 'user-message' : 'assistant-message'}`;
    messageDiv.setAttribute('data-message-id', message.id);

    const isUser = message.role === 'user';
    const contentHtml = isUser ? escapeHtml(message.content) : renderMarkdown(message.content);

    messageDiv.innerHTML = `
        <div class="d-flex ${isUser ? 'justify-content-end' : 'justify-content-start'}">
            <div class="message-bubble ${isUser ? 'bg-primary text-white' : 'bg-light'} p-3 rounded">
                <div class="message-content">${contentHtml}</div>
                <small class="message-time text-muted d-block mt-1">
                    ${new Date(message.timestamp).toLocaleTimeString()}
                </small>
            </div>
        </div>
    `;

    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

// 存储流式消息的原始文本
let streamingMessageContent = {};

// 更新流式消息内容
function updateStreamingMessage(content, messageId) {
    let messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);

    if (!messageDiv) {
        // 创建新的助手消息
        const chatMessages = document.getElementById('chatMessages');
        messageDiv = document.createElement('div');
        messageDiv.className = 'message-item mb-3 assistant-message';
        messageDiv.setAttribute('data-message-id', messageId);
        messageDiv.innerHTML = `
            <div class="d-flex justify-content-start">
                <div class="message-bubble bg-light p-3 rounded">
                    <div class="message-content"></div>
                    <small class="message-time text-muted d-block mt-1">
                        ${new Date().toLocaleTimeString()}
                    </small>
                </div>
            </div>
        `;
        chatMessages.appendChild(messageDiv);
        streamingMessageContent[messageId] = '';
    }

    // 累积原始文本内容
    streamingMessageContent[messageId] += content;

    // 渲染markdown并更新显示
    const contentDiv = messageDiv.querySelector('.message-content');
    contentDiv.innerHTML = renderMarkdown(streamingMessageContent[messageId]);

    scrollToBottom();
}

// 更新消息内容
function updateMessageContent(message) {
    const messageDiv = document.querySelector(`[data-message-id="${message.id}"]`);
    if (messageDiv) {
        const contentDiv = messageDiv.querySelector('.message-content');
        contentDiv.innerHTML = renderMarkdown(message.content);

        // 清理流式消息缓存
        if (streamingMessageContent[message.id]) {
            delete streamingMessageContent[message.id];
        }
    }
}

// 设置生成状态
function setGeneratingStatus(generating) {
    isGenerating = generating;
    const statusIndicator = document.getElementById('statusIndicator');
    const sendButton = document.getElementById('sendButton');
    const messageInput = document.getElementById('messageInput');

    if (generating) {
        statusIndicator.style.display = 'block';
        sendButton.disabled = true;
        messageInput.disabled = true;
        document.getElementById('statusText').textContent = 'AI正在思考...';
    } else {
        statusIndicator.style.display = 'none';
        sendButton.disabled = false;
        messageInput.disabled = false;
    }
}

// 清空聊天
async function clearChat() {
    if (!confirm('确定要清空聊天记录吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/sessions/${sessionId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            document.getElementById('chatMessages').innerHTML = '';
            showToast('聊天记录已清空', 'success');
        }
    } catch (error) {
        console.error('清空聊天失败:', error);
        showToast('清空聊天失败', 'error');
    }
}

// 返回首页
function goHome() {
    window.location.href = '/';
}

// 滚动到底部
function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 切换配置面板
function toggleConfig() {
    const configSidebar = document.getElementById('configSidebar');
    configSidebar.classList.toggle('collapsed');
}

// 加载会话列表
async function loadSessions() {
    try {
        const response = await fetch('/api/sessions');
        const sessions = await response.json();

        const sessionsList = document.getElementById('sessionsList');
        sessionsList.innerHTML = '';

        if (sessions.length === 0) {
            sessionsList.innerHTML = '<div class="text-muted text-center p-3">暂无会话</div>';
            return;
        }

        sessions.forEach(session => {
            const sessionItem = document.createElement('div');
            sessionItem.className = `session-item ${session.id === sessionId ? 'active' : ''}`;
            sessionItem.onclick = () => openSession(session.id);

            const lastMessage = session.last_message || '新会话';
            const preview = lastMessage.length > 30 ? lastMessage.substring(0, 30) + '...' : lastMessage;

            sessionItem.innerHTML = `
                <div class="session-title">${escapeHtml(preview)}</div>
                <div class="session-preview">${session.message_count || 0} 条消息</div>
                <div class="session-time">${new Date(session.timestamp).toLocaleString()}</div>
            `;

            sessionsList.appendChild(sessionItem);
        });
    } catch (error) {
        console.error('加载会话列表失败:', error);
    }
}

// 创建新会话
function createNewSession() {
    const newSessionId = generateUUID();
    openSession(newSessionId);
}

// 打开会话
function openSession(sessionId) {
    if (sessionId !== window.sessionId) {
        window.location.href = `/chat/${sessionId}`;
    }
}

// 生成UUID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// TTS引擎切换相关函数
async function switchTTSEngine() {
    const selectedEngine = document.getElementById('ttsEngine').value;
    const switchBtn = document.getElementById('switchEngineBtn');
    const originalText = switchBtn.innerHTML;

    // 显示切换状态
    switchBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 切换中...';
    switchBtn.disabled = true;
    updateEngineStatus(selectedEngine, 'switching');

    try {
        const response = await fetch(`/api/sessions/${sessionId}/tts/switch-engine`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                engine_type: selectedEngine
            })
        });

        const result = await response.json();

        if (response.ok && result.status === 'success') {
            updateEngineStatus(selectedEngine, 'ready');
            showToast(`已切换到 ${getEngineDisplayName(selectedEngine)}`, 'success');

            // 保存配置
            await saveConfig();
        } else {
            updateEngineStatus(selectedEngine, 'error');
            showToast(`切换失败: ${result.message || '未知错误'}`, 'error');
        }

    } catch (error) {
        updateEngineStatus(selectedEngine, 'error');
        showToast(`切换引擎出错: ${error.message}`, 'error');
    } finally {
        // 恢复按钮状态
        setTimeout(() => {
            switchBtn.innerHTML = originalText;
            switchBtn.disabled = false;
        }, 1000);
    }
}

function updateEngineStatus(engineType, status = 'ready') {
    const statusBadge = document.getElementById('engineStatus');
    const engineName = getEngineDisplayName(engineType);

    switch (status) {
        case 'ready':
            statusBadge.className = 'badge bg-success me-2';
            statusBadge.innerHTML = '<i class="bi bi-check-circle"></i> 已就绪';
            break;
        case 'switching':
            statusBadge.className = 'badge bg-warning me-2';
            statusBadge.innerHTML = '<i class="bi bi-hourglass-split"></i> 切换中';
            break;
        case 'error':
            statusBadge.className = 'badge bg-danger me-2';
            statusBadge.innerHTML = '<i class="bi bi-exclamation-circle"></i> 错误';
            break;
        case 'requires-config':
            statusBadge.className = 'badge bg-warning me-2';
            statusBadge.innerHTML = '<i class="bi bi-gear"></i> 需配置';
            break;
    }
}

function getEngineDisplayName(engineType) {
    const names = {
        'edge_tts': 'Microsoft Edge TTS',
        'f5_tts': 'F5-TTS',
        'megatts3': 'MegaTTS3',
        'cosyvoice2': 'CosyVoice2',
        'openai_tts': 'OpenAI TTS'
    };
    return names[engineType] || engineType;
}

function renderEngineSpecificConfig(engineType, config = {}) {
    const configContainer = document.getElementById('engineSpecificConfig');

    // 清空现有配置
    configContainer.innerHTML = '';

    // 根据引擎类型渲染特定配置
    switch (engineType) {
        case 'openai_tts':
            configContainer.innerHTML = `
                <div class="mb-2">
                    <label class="form-label form-label-sm">OpenAI API密钥</label>
                    <input type="password" class="form-control form-control-sm" id="openaiTtsApiKey"
                           placeholder="输入OpenAI API密钥" value="${config.openai_tts_api_key || ''}">
                    <small class="form-text text-muted">必填：用于调用OpenAI TTS服务</small>
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">模型</label>
                    <select class="form-select form-select-sm" id="openaiTtsModel">
                        <option value="tts-1" ${(config.openai_tts_model || 'tts-1') === 'tts-1' ? 'selected' : ''}>tts-1 (标准)</option>
                        <option value="tts-1-hd" ${config.openai_tts_model === 'tts-1-hd' ? 'selected' : ''}>tts-1-hd (高清)</option>
                    </select>
                </div>
            `;
            break;

        case 'megatts3':
            configContainer.innerHTML = `
                <div class="mb-2">
                    <label class="form-label form-label-sm">API地址</label>
                    <input type="text" class="form-control form-control-sm" id="megatts3ApiUrl"
                           placeholder="http://localhost:8080/api/tts" value="${config.megatts3_api_url || ''}">
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">API密钥</label>
                    <input type="password" class="form-control form-control-sm" id="megatts3ApiKey"
                           placeholder="输入API密钥" value="${config.megatts3_api_key || ''}">
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">模型名称</label>
                    <input type="text" class="form-control form-control-sm" id="megatts3ModelName"
                           placeholder="megatts3-base" value="${config.megatts3_model_name || 'megatts3-base'}">
                </div>
            `;
            break;

        case 'f5_tts':
            configContainer.innerHTML = `
                <div class="mb-2">
                    <label class="form-label form-label-sm">设备</label>
                    <select class="form-select form-select-sm" id="f5TtsDevice">
                        <option value="auto" ${(config.f5_tts_device || 'auto') === 'auto' ? 'selected' : ''}>自动选择</option>
                        <option value="cpu" ${config.f5_tts_device === 'cpu' ? 'selected' : ''}>CPU</option>
                        <option value="cuda" ${config.f5_tts_device === 'cuda' ? 'selected' : ''}>CUDA (GPU)</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">模型路径</label>
                    <input type="text" class="form-control form-control-sm" id="f5TtsModelPath"
                           placeholder="留空使用默认模型" value="${config.f5_tts_model_path || ''}">
                    <small class="form-text text-muted">可选：自定义模型文件路径</small>
                </div>
            `;
            break;

        case 'cosyvoice2':
            configContainer.innerHTML = `
                <div class="mb-2">
                    <label class="form-label form-label-sm">说话人</label>
                    <select class="form-select form-select-sm" id="cosyvoice2Speaker">
                        <option value="zh-CN-female-1" ${(config.cosyvoice2_speaker || 'zh-CN-female-1') === 'zh-CN-female-1' ? 'selected' : ''}>中文女声1</option>
                        <option value="zh-CN-female-2" ${config.cosyvoice2_speaker === 'zh-CN-female-2' ? 'selected' : ''}>中文女声2</option>
                        <option value="zh-CN-male-1" ${config.cosyvoice2_speaker === 'zh-CN-male-1' ? 'selected' : ''}>中文男声1</option>
                        <option value="zh-CN-male-2" ${config.cosyvoice2_speaker === 'zh-CN-male-2' ? 'selected' : ''}>中文男声2</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">情感</label>
                    <select class="form-select form-select-sm" id="cosyvoice2Emotion">
                        <option value="neutral" ${(config.cosyvoice2_emotion || 'neutral') === 'neutral' ? 'selected' : ''}>中性</option>
                        <option value="happy" ${config.cosyvoice2_emotion === 'happy' ? 'selected' : ''}>开心</option>
                        <option value="sad" ${config.cosyvoice2_emotion === 'sad' ? 'selected' : ''}>悲伤</option>
                        <option value="angry" ${config.cosyvoice2_emotion === 'angry' ? 'selected' : ''}>愤怒</option>
                    </select>
                </div>
                <div class="mb-2">
                    <label class="form-label form-label-sm">设备</label>
                    <select class="form-select form-select-sm" id="cosyvoice2Device">
                        <option value="auto" ${(config.cosyvoice2_device || 'auto') === 'auto' ? 'selected' : ''}>自动选择</option>
                        <option value="cpu" ${config.cosyvoice2_device === 'cpu' ? 'selected' : ''}>CPU</option>
                        <option value="cuda" ${config.cosyvoice2_device === 'cuda' ? 'selected' : ''}>CUDA (GPU)</option>
                    </select>
                </div>
            `;
            break;

        default:
            // Edge TTS 和其他引擎不需要特殊配置
            configContainer.innerHTML = '<small class="text-muted">此引擎无需额外配置</small>';
            break;
    }
}

// 页面卸载时关闭连接
window.addEventListener('beforeunload', function() {
    if (websocket) {
        websocket.close();
    }
    if (eventSource) {
        eventSource.close();
    }
});
</script>
{% endblock %}