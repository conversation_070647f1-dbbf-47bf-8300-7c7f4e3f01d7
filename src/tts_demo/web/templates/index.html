{% extends "base.html" %}

{% block title %}VoiceFlow Studio - 智能语音对话平台{% endblock %}

{% block page_class %}homepage{% endblock %}

{% block extra_head %}
<style>
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 80px 0;
        margin-bottom: 0;
    }

    .hero-content {
        max-width: 800px;
        margin: 0 auto;
    }

    .feature-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .feature-icon {
        width: 4rem;
        height: 4rem;
        transition: all 0.3s ease;
    }

    .feature-card:hover .feature-icon {
        transform: scale(1.1);
    }

    .engine-showcase {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        border-radius: 20px;
        padding: 40px;
        margin: 60px 0;
    }

    .engine-item {
        transition: all 0.3s ease;
    }

    .engine-item:hover {
        transform: translateY(-3px);
    }

    .engine-logo {
        width: 3.5rem;
        height: 3.5rem;
        transition: all 0.3s ease;
    }

    .engine-item:hover .engine-logo {
        transform: scale(1.1);
    }

    .stats-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0;
        margin: 60px 0;
        border-radius: 20px;
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .cta-section {
        padding: 60px 0;
    }

    .btn-hero {
        padding: 15px 30px;
        font-size: 1.1rem;
        border-radius: 50px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .btn-hero:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .section-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="section-container">
        <div class="hero-content text-center">
            <div class="mb-4">
                <i class="bi bi-soundwave display-1 mb-3" style="font-size: 5rem;"></i>
            </div>
            <h1 class="display-3 fw-bold mb-4">VoiceFlow Studio</h1>
            <p class="lead fs-4 mb-4">
                下一代智能语音对话平台<br>
                集成多引擎TTS • 实时语音合成 • 情感表达
            </p>
            <div class="alert alert-light d-inline-block mb-4 text-dark">
                <i class="bi bi-star-fill text-warning me-2"></i>
                <strong>最新功能</strong>：5种TTS引擎 • Web界面切换 • 情感语音合成
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<div class="section-container">
    <!-- Features Section -->
    <section class="py-5">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold text-primary mb-3">核心功能特性</h2>
            <p class="lead text-muted">强大的功能，简单的操作，专业的体验</p>
        </div>

        <!-- 功能卡片网格 -->
        <div class="row g-4 mb-5">
            <!-- 多引擎TTS支持 -->
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-collection fs-2"></i>
                        </div>
                        <h5 class="card-title text-primary fw-bold">多引擎TTS支持</h5>
                        <p class="card-text text-muted">支持5种主流TTS引擎，满足不同场景需求</p>
                        <div class="mt-3">
                            <span class="badge bg-success me-1">Edge TTS</span>
                            <span class="badge bg-info me-1">F5-TTS</span>
                            <span class="badge bg-warning me-1">OpenAI</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时引擎切换 -->
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-success bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-arrow-repeat fs-2"></i>
                        </div>
                        <h5 class="card-title text-success fw-bold">实时引擎切换</h5>
                        <p class="card-text text-muted">Web界面一键切换，实时状态监控</p>
                        <div class="mt-3">
                            <span class="badge bg-light text-dark">一键切换</span>
                            <span class="badge bg-light text-dark">状态监控</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 情感语音合成 -->
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-warning bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-emoji-smile fs-2"></i>
                        </div>
                        <h5 class="card-title text-warning fw-bold">情感语音合成</h5>
                        <p class="card-text text-muted">支持多种情感表达和说话人选择</p>
                        <div class="mt-3">
                            <span class="badge bg-light text-dark">😊 开心</span>
                            <span class="badge bg-light text-dark">😢 悲伤</span>
                            <span class="badge bg-light text-dark">😠 愤怒</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能对话系统 -->
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-info bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-lightning-charge fs-2"></i>
                        </div>
                        <h5 class="card-title text-info fw-bold">智能对话系统</h5>
                        <p class="card-text text-muted">实时流式对话，双LLM源支持</p>
                        <div class="mt-3">
                            <span class="badge bg-light text-dark">流式响应</span>
                            <span class="badge bg-light text-dark">双LLM源</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 高级配置 -->
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-secondary bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-sliders fs-2"></i>
                        </div>
                        <h5 class="card-title text-secondary fw-bold">高级配置</h5>
                        <p class="card-text text-muted">丰富的参数配置和断句优化</p>
                        <div class="mt-3">
                            <span class="badge bg-light text-dark">语音配置</span>
                            <span class="badge bg-light text-dark">断句优化</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 性能优化 -->
            <div class="col-lg-4 col-md-6">
                <div class="card feature-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-dark bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center">
                            <i class="bi bi-speedometer2 fs-2"></i>
                        </div>
                        <h5 class="card-title text-dark fw-bold">性能优化</h5>
                        <p class="card-text text-muted">智能缓存和队列管理，流畅体验</p>
                        <div class="mt-3">
                            <span class="badge bg-light text-dark">智能缓存</span>
                            <span class="badge bg-light text-dark">队列管理</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- TTS引擎展示 -->
    <section class="engine-showcase">
        <div class="text-center mb-4">
            <h2 class="display-6 fw-bold text-primary mb-3">
                <i class="bi bi-gear-fill me-2"></i>
                支持的TTS引擎
            </h2>
            <p class="lead text-muted">多样化的引擎选择，满足不同场景需求</p>
        </div>
        <div class="row g-4">
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="engine-item text-center">
                    <div class="engine-logo bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="bi bi-microsoft fs-4"></i>
                    </div>
                    <h6 class="fw-bold">Edge TTS</h6>
                    <p class="text-muted small">微软免费TTS</p>
                    <span class="badge bg-success">免费</span>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="engine-item text-center">
                    <div class="engine-logo bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="bi bi-code-square fs-4"></i>
                    </div>
                    <h6 class="fw-bold">F5-TTS</h6>
                    <p class="text-muted small">开源高质量TTS</p>
                    <span class="badge bg-info">开源</span>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="engine-item text-center">
                    <div class="engine-logo bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="bi bi-robot fs-4"></i>
                    </div>
                    <h6 class="fw-bold">OpenAI TTS</h6>
                    <p class="text-muted small">商业级语音合成</p>
                    <span class="badge bg-warning">商业</span>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="engine-item text-center">
                    <div class="engine-logo bg-danger text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="bi bi-heart-fill fs-4"></i>
                    </div>
                    <h6 class="fw-bold">CosyVoice2</h6>
                    <p class="text-muted small">情感语音合成</p>
                    <span class="badge bg-danger">情感</span>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="engine-item text-center">
                    <div class="engine-logo bg-secondary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="bi bi-lightning-fill fs-4"></i>
                    </div>
                    <h6 class="fw-bold">MegaTTS3</h6>
                    <p class="text-muted small">高性能TTS引擎</p>
                    <span class="badge bg-secondary">高性能</span>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="engine-item text-center">
                    <div class="engine-logo bg-dark text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="bi bi-plus-circle fs-4"></i>
                    </div>
                    <h6 class="fw-bold">更多引擎</h6>
                    <p class="text-muted small">持续扩展中</p>
                    <span class="badge bg-dark">扩展</span>
                </div>
            </div>
        </div>
    </section>

    <!-- 统计数据 -->
    <section class="stats-section text-center">
        <div class="row g-4">
            <div class="col-md-3 col-6">
                <div class="stat-number">5+</div>
                <p class="mb-0 fs-5">TTS引擎</p>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-number">2</div>
                <p class="mb-0 fs-5">LLM源</p>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-number">10+</div>
                <p class="mb-0 fs-5">语音选择</p>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-number">∞</div>
                <p class="mb-0 fs-5">对话会话</p>
            </div>
        </div>
    </section>

    <!-- 行动按钮组 -->
    <section class="cta-section text-center">
        <div class="mb-4">
            <h2 class="display-6 fw-bold text-primary mb-3">开始您的语音对话之旅</h2>
            <p class="lead text-muted">选择您喜欢的方式，立即体验VoiceFlow Studio</p>
        </div>
        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
            <button class="btn btn-primary btn-hero" onclick="createNewSession()">
                <i class="bi bi-chat-dots me-2"></i>
                开始智能对话
            </button>
            <button class="btn btn-outline-primary btn-hero" onclick="showDemo()">
                <i class="bi bi-play-circle me-2"></i>
                查看演示
            </button>
            <button class="btn btn-outline-secondary btn-hero" onclick="showDocumentation()">
                <i class="bi bi-book me-2"></i>
                使用文档
            </button>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_scripts %}
<script>

// 创建新会话
function createNewSession() {
    const sessionId = generateUUID();
    window.location.href = `/chat/${sessionId}`;
}

// 生成UUID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 显示演示
function showDemo() {
    window.open('/static/demo_engine_switch.html', '_blank');
}

// 显示文档
function showDocumentation() {
    alert('文档功能开发中，请查看项目README.md');
}
</script>
{% endblock %}
