{% extends "base.html" %}

{% block title %}TTS Demo 聊天界面 - 首页{% endblock %}

{% block content %}
<div class="d-flex chat-container">
    <!-- 左侧会话列表 -->
    <div class="sessions-sidebar">
        <div class="p-3 border-bottom">
            <h6 class="mb-0">
                <i class="bi bi-chat-left-text"></i>
                会话列表
            </h6>
        </div>
        <div id="sessionList">
            <!-- 会话列表将通过JavaScript动态加载 -->
        </div>
        <div class="p-3 border-top">
            <button class="btn btn-outline-primary btn-sm w-100" onclick="createNewSession()">
                <i class="bi bi-plus"></i>
                新建会话
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="chat-main d-flex flex-column justify-content-center align-items-center">
        <div class="text-center">
            <i class="bi bi-robot display-1 text-primary mb-4"></i>
            <h1 class="text-primary mb-3">🎤 TTS Demo 智能聊天平台</h1>
            <p class="lead text-muted mb-4">
                集成多引擎TTS语音合成的智能对话平台<br>
                支持5种主流TTS引擎 • 实时语音合成 • 智能对话体验
            </p>

            <!-- 版本信息和亮点 -->
            <div class="alert alert-info d-inline-block mb-4">
                <i class="bi bi-star-fill text-warning"></i>
                <strong>最新功能</strong>：多TTS引擎支持 • Web界面引擎切换 • 情感语音合成
            </div>

            <!-- 核心功能展示 -->
            <div class="row g-4 mb-5">
                <!-- 多引擎TTS支持 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-primary">
                        <div class="card-body text-center">
                            <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center" style="width: 4rem; height: 4rem;">
                                <i class="bi bi-collection fs-2"></i>
                            </div>
                            <h5 class="card-title text-primary">多引擎TTS支持</h5>
                            <p class="card-text text-muted">支持5种主流TTS引擎：Edge TTS、F5-TTS、OpenAI TTS、CosyVoice2、MegaTTS3</p>
                            <div class="mt-2">
                                <span class="badge bg-success me-1">Edge TTS</span>
                                <span class="badge bg-info me-1">F5-TTS</span>
                                <span class="badge bg-warning me-1">OpenAI</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时引擎切换 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-success">
                        <div class="card-body text-center">
                            <div class="feature-icon bg-success bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center" style="width: 4rem; height: 4rem;">
                                <i class="bi bi-arrow-repeat fs-2"></i>
                            </div>
                            <h5 class="card-title text-success">实时引擎切换</h5>
                            <p class="card-text text-muted">Web界面一键切换TTS引擎，实时状态监控，引擎特定配置</p>
                            <div class="mt-2">
                                <span class="badge bg-light text-dark">一键切换</span>
                                <span class="badge bg-light text-dark">状态监控</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 情感语音合成 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-warning">
                        <div class="card-body text-center">
                            <div class="feature-icon bg-warning bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center" style="width: 4rem; height: 4rem;">
                                <i class="bi bi-emoji-smile fs-2"></i>
                            </div>
                            <h5 class="card-title text-warning">情感语音合成</h5>
                            <p class="card-text text-muted">支持多种情感表达：开心、悲伤、愤怒等，多说话人选择</p>
                            <div class="mt-2">
                                <span class="badge bg-light text-dark">😊 开心</span>
                                <span class="badge bg-light text-dark">😢 悲伤</span>
                                <span class="badge bg-light text-dark">😠 愤怒</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 智能对话系统 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-info">
                        <div class="card-body text-center">
                            <div class="feature-icon bg-info bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center" style="width: 4rem; height: 4rem;">
                                <i class="bi bi-lightning-charge fs-2"></i>
                            </div>
                            <h5 class="card-title text-info">智能对话系统</h5>
                            <p class="card-text text-muted">实时流式对话，支持OpenAI API和Langflow工作流</p>
                            <div class="mt-2">
                                <span class="badge bg-light text-dark">流式响应</span>
                                <span class="badge bg-light text-dark">双LLM源</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 高级配置 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-secondary">
                        <div class="card-body text-center">
                            <div class="feature-icon bg-secondary bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center" style="width: 4rem; height: 4rem;">
                                <i class="bi bi-sliders fs-2"></i>
                            </div>
                            <h5 class="card-title text-secondary">高级配置</h5>
                            <p class="card-text text-muted">丰富的参数配置：语音、语速、音量、断句模式等</p>
                            <div class="mt-2">
                                <span class="badge bg-light text-dark">语音配置</span>
                                <span class="badge bg-light text-dark">断句优化</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 性能优化 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-dark">
                        <div class="card-body text-center">
                            <div class="feature-icon bg-dark bg-gradient text-white rounded-circle mb-3 mx-auto d-flex align-items-center justify-content-center" style="width: 4rem; height: 4rem;">
                                <i class="bi bi-speedometer2 fs-2"></i>
                            </div>
                            <h5 class="card-title text-dark">性能优化</h5>
                            <p class="card-text text-muted">智能缓存、队列管理、资源优化，确保流畅体验</p>
                            <div class="mt-2">
                                <span class="badge bg-light text-dark">智能缓存</span>
                                <span class="badge bg-light text-dark">队列管理</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TTS引擎展示 -->
            <div class="bg-light rounded-3 p-4 mb-4">
                <h4 class="text-center mb-3">
                    <i class="bi bi-gear-fill text-primary"></i>
                    支持的TTS引擎
                </h4>
                <div class="row g-3">
                    <div class="col-md-2 col-sm-4 col-6">
                        <div class="text-center">
                            <div class="engine-logo bg-success text-white rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 3rem; height: 3rem;">
                                <i class="bi bi-microsoft"></i>
                            </div>
                            <small class="text-muted">Edge TTS</small>
                            <div><span class="badge bg-success">免费</span></div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4 col-6">
                        <div class="text-center">
                            <div class="engine-logo bg-info text-white rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 3rem; height: 3rem;">
                                <i class="bi bi-code-square"></i>
                            </div>
                            <small class="text-muted">F5-TTS</small>
                            <div><span class="badge bg-info">开源</span></div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4 col-6">
                        <div class="text-center">
                            <div class="engine-logo bg-warning text-white rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 3rem; height: 3rem;">
                                <i class="bi bi-robot"></i>
                            </div>
                            <small class="text-muted">OpenAI TTS</small>
                            <div><span class="badge bg-warning">商业</span></div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4 col-6">
                        <div class="text-center">
                            <div class="engine-logo bg-danger text-white rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 3rem; height: 3rem;">
                                <i class="bi bi-heart-fill"></i>
                            </div>
                            <small class="text-muted">CosyVoice2</small>
                            <div><span class="badge bg-danger">情感</span></div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4 col-6">
                        <div class="text-center">
                            <div class="engine-logo bg-secondary text-white rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 3rem; height: 3rem;">
                                <i class="bi bi-lightning-fill"></i>
                            </div>
                            <small class="text-muted">MegaTTS3</small>
                            <div><span class="badge bg-secondary">高性能</span></div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4 col-6">
                        <div class="text-center">
                            <div class="engine-logo bg-dark text-white rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 3rem; height: 3rem;">
                                <i class="bi bi-plus-circle"></i>
                            </div>
                            <small class="text-muted">更多引擎</small>
                            <div><span class="badge bg-dark">扩展</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行动按钮组 -->
            <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                <button class="btn btn-primary btn-lg px-4" onclick="createNewSession()">
                    <i class="bi bi-chat-dots"></i>
                    开始智能对话
                </button>
                <button class="btn btn-outline-primary btn-lg px-4" onclick="showDemo()">
                    <i class="bi bi-play-circle"></i>
                    查看演示
                </button>
                <button class="btn btn-outline-secondary btn-lg px-4" onclick="showDocumentation()">
                    <i class="bi bi-book"></i>
                    使用文档
                </button>
            </div>

            <!-- 快速统计 -->
            <div class="row g-3 mt-4 text-center">
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <h3 class="text-primary mb-1">5+</h3>
                        <small class="text-muted">TTS引擎</small>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <h3 class="text-success mb-1">2</h3>
                        <small class="text-muted">LLM源</small>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <h3 class="text-warning mb-1">10+</h3>
                        <small class="text-muted">语音选择</small>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <h3 class="text-info mb-1">∞</h3>
                        <small class="text-muted">对话会话</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 页面加载时获取会话列表
document.addEventListener('DOMContentLoaded', function() {
    loadSessions();
});

// 加载会话列表
async function loadSessions() {
    try {
        const response = await fetch('/api/sessions');
        const sessions = await response.json();

        const sessionList = document.getElementById('sessionList');
        sessionList.innerHTML = '';

        if (sessions.length === 0) {
            sessionList.innerHTML = '<div class="text-muted text-center p-3">暂无会话</div>';
            return;
        }

        sessions.forEach(session => {
            const sessionItem = document.createElement('div');
            sessionItem.className = 'session-item';
            sessionItem.onclick = () => openSession(session.id);

            const lastMessage = session.last_message || '新会话';
            const preview = lastMessage.length > 30 ? lastMessage.substring(0, 30) + '...' : lastMessage;

            sessionItem.innerHTML = `
                <div class="session-title">${preview}</div>
                <div class="session-preview">${session.message_count || 0} 条消息</div>
                <div class="session-time">${new Date(session.timestamp).toLocaleString()}</div>
            `;

            sessionList.appendChild(sessionItem);
        });
    } catch (error) {
        console.error('加载会话列表失败:', error);
        showToast('加载会话列表失败', 'error');
    }
}

// 创建新会话
function createNewSession() {
    // 生成新的会话ID
    const sessionId = generateUUID();
    openSession(sessionId);
}

// 打开会话
function openSession(sessionId) {
    window.location.href = `/chat/${sessionId}`;
}

// 删除会话
async function deleteSession(sessionId) {
    if (!confirm('确定要删除这个会话吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/sessions/${sessionId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showToast('会话已删除', 'success');
            loadSessions(); // 重新加载会话列表
        } else {
            throw new Error('删除失败');
        }
    } catch (error) {
        console.error('删除会话失败:', error);
        showToast('删除会话失败', 'error');
    }
}

// 生成UUID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 显示演示
function showDemo() {
    // 打开演示页面
    window.open('/static/demo_engine_switch.html', '_blank');
}

// 显示文档
function showDocumentation() {
    // 可以链接到GitHub文档或本地文档
    showToast('文档功能开发中，请查看项目README.md', 'info');
}

// Toast提示函数
function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    const toastBody = toast.querySelector('.toast-body');
    const toastHeader = toast.querySelector('.toast-header');

    // 设置图标和颜色
    const iconMap = {
        'success': 'bi-check-circle text-success',
        'error': 'bi-exclamation-circle text-danger',
        'warning': 'bi-exclamation-triangle text-warning',
        'info': 'bi-info-circle text-primary'
    };

    const icon = toastHeader.querySelector('i');
    icon.className = `${iconMap[type] || iconMap.info} me-2`;

    toastBody.textContent = message;

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}
</script>
{% endblock %}
