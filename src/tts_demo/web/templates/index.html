{% extends "base.html" %}

{% block title %}TTS Demo 聊天界面 - 首页{% endblock %}

{% block content %}
<div class="d-flex chat-container">
    <!-- 左侧会话列表 -->
    <div class="sessions-sidebar">
        <div class="p-3 border-bottom">
            <h6 class="mb-0">
                <i class="bi bi-chat-left-text"></i>
                会话列表
            </h6>
        </div>
        <div id="sessionList">
            <!-- 会话列表将通过JavaScript动态加载 -->
        </div>
        <div class="p-3 border-top">
            <button class="btn btn-outline-primary btn-sm w-100" onclick="createNewSession()">
                <i class="bi bi-plus"></i>
                新建会话
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="chat-main d-flex flex-column justify-content-center align-items-center">
        <div class="text-center">
            <i class="bi bi-chat-dots display-1 text-muted mb-4"></i>
            <h2 class="text-muted mb-3">欢迎使用 TTS Demo 聊天界面</h2>
            <p class="text-muted mb-4">
                支持 OpenAI 和 Langflow 两种 LLM 源的实时聊天界面<br>
                具备流式对话和 TTS 语音合成功能
            </p>

            <!-- 功能特点 -->
            <div class="row g-3 mb-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-lightning-charge text-primary fs-1"></i>
                            <h5 class="card-title mt-2">实时流式对话</h5>
                            <p class="card-text text-muted">支持实时流式文本生成，即时显示AI回复</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-volume-up text-success fs-1"></i>
                            <h5 class="card-title mt-2">TTS 语音合成</h5>
                            <p class="card-text text-muted">支持多种语音和语速设置的实时语音合成</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-gear text-warning fs-1"></i>
                            <h5 class="card-title mt-2">双 LLM 支持</h5>
                            <p class="card-text text-muted">支持 OpenAI API 和 Langflow 工作流</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="bi bi-sliders text-info fs-1"></i>
                            <h5 class="card-title mt-2">丰富配置</h5>
                            <p class="card-text text-muted">支持语音、语速、模型等多种参数配置</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 开始按钮 -->
            <button class="btn btn-primary btn-lg" onclick="createNewSession()">
                <i class="bi bi-chat-dots"></i>
                开始聊天
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 页面加载时获取会话列表
document.addEventListener('DOMContentLoaded', function() {
    loadSessions();
});

// 加载会话列表
async function loadSessions() {
    try {
        const response = await fetch('/api/sessions');
        const sessions = await response.json();

        const sessionList = document.getElementById('sessionList');
        sessionList.innerHTML = '';

        if (sessions.length === 0) {
            sessionList.innerHTML = '<div class="text-muted text-center p-3">暂无会话</div>';
            return;
        }

        sessions.forEach(session => {
            const sessionItem = document.createElement('div');
            sessionItem.className = 'session-item';
            sessionItem.onclick = () => openSession(session.id);

            const lastMessage = session.last_message || '新会话';
            const preview = lastMessage.length > 30 ? lastMessage.substring(0, 30) + '...' : lastMessage;

            sessionItem.innerHTML = `
                <div class="session-title">${preview}</div>
                <div class="session-preview">${session.message_count || 0} 条消息</div>
                <div class="session-time">${new Date(session.timestamp).toLocaleString()}</div>
            `;

            sessionList.appendChild(sessionItem);
        });
    } catch (error) {
        console.error('加载会话列表失败:', error);
        showToast('加载会话列表失败', 'error');
    }
}

// 创建新会话
function createNewSession() {
    // 生成新的会话ID
    const sessionId = generateUUID();
    openSession(sessionId);
}

// 打开会话
function openSession(sessionId) {
    window.location.href = `/chat/${sessionId}`;
}

// 删除会话
async function deleteSession(sessionId) {
    if (!confirm('确定要删除这个会话吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/sessions/${sessionId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showToast('会话已删除', 'success');
            loadSessions(); // 重新加载会话列表
        } else {
            throw new Error('删除失败');
        }
    } catch (error) {
        console.error('删除会话失败:', error);
        showToast('删除会话失败', 'error');
    }
}

// 生成UUID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}
</script>
{% endblock %}
