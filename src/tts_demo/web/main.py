#!/usr/bin/env python3
"""
Web应用主入口

提供可以被uvicorn直接导入的应用实例，支持热重载功能
"""

import sys
from pathlib import Path

# 确保可以导入tts_demo模块
src_path = Path(__file__).parent.parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from tts_demo.web.app import create_app
from tts_demo.config import get_app_config

# 创建应用实例
config = get_app_config()
app = create_app(config.web)

# 导出应用实例供uvicorn使用
__all__ = ["app"]


def main():
    """直接运行Web应用"""
    import uvicorn
    
    uvicorn.run(
        "tts_demo.web.main:app",
        host=config.web.host,
        port=config.web.port,
        reload=config.web.reload,
        log_level=config.web.log_level,
        reload_dirs=[str(Path(__file__).parent.parent)]
    )


if __name__ == "__main__":
    main()
