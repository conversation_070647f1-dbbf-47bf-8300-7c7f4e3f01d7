<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS多引擎切换演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .engine-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .engine-card {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .engine-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
        }
        .engine-card.active {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        .engine-card.disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .engine-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .engine-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .engine-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }
        .status-available {
            background-color: #d4edda;
            color: #155724;
        }
        .status-requires-config {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-active {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .test-section {
            margin-top: 30px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .status-display {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: bold;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.info {
            color: #007bff;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .config-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .config-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 TTS多引擎切换演示</h1>
            <p>体验不同TTS引擎的语音合成效果，支持实时切换</p>
        </div>
        
        <div class="status-display status-info" id="connectionStatus">
            <span id="statusText">正在连接服务器...</span>
        </div>
    </div>

    <div class="container">
        <h3>🔧 选择TTS引擎</h3>
        <div class="engine-grid" id="engineGrid">
            <!-- 引擎卡片将通过JavaScript动态生成 -->
        </div>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>🎯 测试语音合成</h3>
            <textarea class="test-input" id="testText" rows="3" 
                      placeholder="输入要测试的文本...">你好！这是一个TTS引擎测试。我们正在测试不同引擎的语音合成效果。</textarea>
            
            <div>
                <button class="btn btn-success" onclick="testCurrentEngine()" id="testBtn">
                    🎵 测试当前引擎
                </button>
                <button class="btn" onclick="stopTTS()">
                    ⏹️ 停止播放
                </button>
                <button class="btn" onclick="clearLog()">
                    🗑️ 清空日志
                </button>
            </div>
            
            <div class="config-section" id="engineConfig" style="display: none;">
                <h4>引擎配置</h4>
                <div id="configInputs">
                    <!-- 配置输入框将动态生成 -->
                </div>
                <button class="btn" onclick="saveEngineConfig()">保存配置</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>📋 操作日志</h3>
        <div class="log" id="logContainer">
            <div class="log-entry info">等待开始测试...</div>
        </div>
    </div>

    <script>
        const sessionId = 'engine-demo-' + Date.now();
        const baseUrl = window.location.origin;
        let currentEngine = 'edge_tts';
        let availableEngines = [];

        // 引擎信息
        const engineInfo = {
            'edge_tts': {
                name: 'Microsoft Edge TTS',
                description: '免费、高质量、支持多语言的TTS服务',
                status: 'available',
                requiresConfig: false
            },
            'f5_tts': {
                name: 'F5-TTS',
                description: '开源高质量TTS模型，支持自然语音合成',
                status: 'available',
                requiresConfig: false
            },
            'megatts3': {
                name: 'MegaTTS3',
                description: '高性能TTS模型，支持多语言',
                status: 'requires-config',
                requiresConfig: true,
                configFields: ['api_url', 'api_key', 'model_name']
            },
            'cosyvoice2': {
                name: 'CosyVoice2',
                description: '阿里巴巴开源TTS，支持情感语音合成',
                status: 'available',
                requiresConfig: false
            },
            'openai_tts': {
                name: 'OpenAI TTS',
                description: '商业级高质量语音合成服务',
                status: 'requires-config',
                requiresConfig: true,
                configFields: ['api_key', 'model', 'base_url']
            }
        };

        // 日志记录
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新连接状态
        function updateConnectionStatus(connected, message) {
            const statusElement = document.getElementById('connectionStatus');
            const textElement = document.getElementById('statusText');
            
            if (connected) {
                statusElement.className = 'status-display status-success';
                textElement.textContent = message || '已连接到服务器';
            } else {
                statusElement.className = 'status-display status-error';
                textElement.textContent = message || '无法连接到服务器';
            }
        }

        // 渲染引擎网格
        function renderEngineGrid() {
            const grid = document.getElementById('engineGrid');
            grid.innerHTML = '';

            availableEngines.forEach(engineId => {
                const info = engineInfo[engineId] || {
                    name: engineId.replace('_', ' ').toUpperCase(),
                    description: '未知引擎',
                    status: 'available'
                };

                const card = document.createElement('div');
                card.className = `engine-card ${engineId === currentEngine ? 'active' : ''}`;
                card.onclick = () => switchEngine(engineId);

                let statusClass = 'status-available';
                let statusText = '可用';
                
                if (info.status === 'requires-config') {
                    statusClass = 'status-requires-config';
                    statusText = '需要配置';
                } else if (engineId === currentEngine) {
                    statusClass = 'status-active';
                    statusText = '当前使用';
                }

                card.innerHTML = `
                    <div class="engine-name">${info.name}</div>
                    <div class="engine-description">${info.description}</div>
                    <div class="engine-status ${statusClass}">${statusText}</div>
                `;

                grid.appendChild(card);
            });
        }

        // 切换引擎
        async function switchEngine(engineId) {
            if (engineId === currentEngine) return;

            addLog(`正在切换到引擎: ${engineId}`, 'info');
            
            try {
                const response = await fetch(`${baseUrl}/api/sessions/${sessionId}/tts/switch-engine`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        engine_type: engineId
                    })
                });

                const result = await response.json();
                
                if (response.ok && result.status === 'success') {
                    currentEngine = engineId;
                    addLog(`✓ 成功切换到: ${engineInfo[engineId]?.name || engineId}`, 'success');
                    renderEngineGrid();
                    showEngineConfig(engineId);
                } else {
                    addLog(`✗ 切换失败: ${result.message || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                addLog(`✗ 切换引擎出错: ${error.message}`, 'error');
            }
        }

        // 显示引擎配置
        function showEngineConfig(engineId) {
            const info = engineInfo[engineId];
            const configSection = document.getElementById('engineConfig');
            const configInputs = document.getElementById('configInputs');
            
            if (info && info.requiresConfig && info.configFields) {
                configInputs.innerHTML = '';
                
                info.configFields.forEach(field => {
                    const div = document.createElement('div');
                    div.innerHTML = `
                        <label for="${field}">${field.replace('_', ' ').toUpperCase()}:</label>
                        <input type="text" class="config-input" id="config_${field}" 
                               placeholder="请输入${field}">
                    `;
                    configInputs.appendChild(div);
                });
                
                configSection.style.display = 'block';
            } else {
                configSection.style.display = 'none';
            }
        }

        // 保存引擎配置
        async function saveEngineConfig() {
            const info = engineInfo[currentEngine];
            if (!info || !info.configFields) return;
            
            const config = {};
            info.configFields.forEach(field => {
                const input = document.getElementById(`config_${field}`);
                if (input && input.value.trim()) {
                    config[field] = input.value.trim();
                }
            });
            
            addLog(`保存${currentEngine}配置: ${JSON.stringify(config)}`, 'info');
            // 这里可以添加保存配置的API调用
        }

        // 测试当前引擎
        async function testCurrentEngine() {
            const text = document.getElementById('testText').value.trim();
            if (!text) {
                addLog('请输入测试文本', 'error');
                return;
            }

            addLog(`开始测试引擎: ${engineInfo[currentEngine]?.name || currentEngine}`, 'info');
            addLog(`测试文本: ${text.substring(0, 50)}${text.length > 50 ? '...' : ''}`, 'info');

            try {
                const response = await fetch(`${baseUrl}/api/sessions/${sessionId}/test-tts`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    addLog(`✓ 测试完成: ${result.message}`, 'success');
                } else {
                    addLog(`✗ 测试失败: ${result.detail || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                addLog(`✗ 测试出错: ${error.message}`, 'error');
            }
        }

        // 停止TTS
        async function stopTTS() {
            try {
                const response = await fetch(`${baseUrl}/api/sessions/${sessionId}/tts/stop`, {
                    method: 'POST'
                });

                const result = await response.json();
                addLog(`TTS已停止: ${result.message}`, 'info');
                
            } catch (error) {
                addLog(`停止TTS出错: ${error.message}`, 'error');
            }
        }

        // 清空日志
        function clearLog() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div class="log-entry info">日志已清空</div>';
        }

        // 获取可用引擎
        async function loadAvailableEngines() {
            try {
                const response = await fetch(`${baseUrl}/api/tts/engines`);
                const result = await response.json();
                
                if (response.ok && result.engines) {
                    availableEngines = result.engines.map(engine => engine.id);
                    addLog(`加载了 ${availableEngines.length} 个可用引擎`, 'success');
                    renderEngineGrid();
                } else {
                    // 使用默认引擎列表
                    availableEngines = Object.keys(engineInfo);
                    addLog('使用默认引擎列表', 'info');
                    renderEngineGrid();
                }
                
            } catch (error) {
                addLog(`加载引擎列表失败: ${error.message}`, 'error');
                availableEngines = Object.keys(engineInfo);
                renderEngineGrid();
            }
        }

        // 检查服务器连接
        async function checkConnection() {
            try {
                const response = await fetch(`${baseUrl}/api/sessions`);
                if (response.ok) {
                    updateConnectionStatus(true);
                    return true;
                } else {
                    updateConnectionStatus(false, `服务器响应错误: ${response.status}`);
                    return false;
                }
            } catch (error) {
                updateConnectionStatus(false, `连接失败: ${error.message}`);
                return false;
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', async function() {
            addLog('页面加载完成，正在初始化...', 'info');
            
            const connected = await checkConnection();
            if (connected) {
                await loadAvailableEngines();
                addLog('初始化完成，可以开始测试', 'success');
            } else {
                addLog('服务器连接失败，请确保服务器正在运行', 'error');
                // 仍然显示引擎列表，但功能可能不可用
                availableEngines = Object.keys(engineInfo);
                renderEngineGrid();
            }
        });
    </script>
</body>
</html>
