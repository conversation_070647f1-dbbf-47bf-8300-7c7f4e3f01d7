/* TTS Demo 聊天界面样式 */

/* 全局样式 */
html, body {
    height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    overflow: hidden; /* 防止页面级别的滚动条 */
}

/* 导航栏固定高度 */
.navbar {
    height: 60px; /* 固定导航栏高度 */
    min-height: 60px;
    flex-shrink: 0; /* 防止导航栏被压缩 */
}

/* 主容器高度计算 */
main.container-fluid {
    height: calc(100vh - 60px); /* 减去导航栏高度 */
    max-height: calc(100vh - 60px);
    padding: 0;
    overflow: hidden; /* 防止主容器滚动条 */
    display: flex;
    flex-direction: column;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
}

/* 侧边栏样式 */
.bg-light {
    background-color: #f8f9fa !important;
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}

/* 配置面板样式 */
.config-section {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
}

.config-section h6 {
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #495057;
}

.form-label-sm {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.form-control-sm, .form-select-sm {
    font-size: 0.875rem;
}

/* 聊天消息样式 */
#chatMessages {
    background-color: #fafafa;
    background-image:
        radial-gradient(circle at 1px 1px, rgba(0,0,0,.15) 1px, transparent 0);
    background-size: 20px 20px;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 聊天消息区域滚动条样式 */
#chatMessages::-webkit-scrollbar {
    width: 6px;
}

#chatMessages::-webkit-scrollbar-track {
    background: transparent;
}

#chatMessages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

#chatMessages::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

.message-item {
    animation: fadeInUp 0.3s ease-out;
    margin-bottom: 0.75rem !important;  /* 减少消息间距 */
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-bubble {
    max-width: 75%;          /* 增加最大宽度，显示更多内容 */
    word-wrap: break-word;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
    padding: 0.6rem 0.8rem !important;  /* 减少内边距，更紧凑 */
}

.user-message .message-bubble {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-bottom-right-radius: 0.25rem !important;
}

.assistant-message .message-bubble {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-bottom-left-radius: 0.25rem !important;
}

.message-content {
    line-height: 1.4;        /* 减少行高，更紧凑 */
    white-space: pre-wrap;
    font-size: 0.95rem;      /* 稍微减小字体，增加信息密度 */
}

.message-time {
    font-size: 0.7rem;      /* 减小时间戳字体 */
    opacity: 0.7;           /* 降低透明度，更不显眼 */
    margin-top: 0.2rem;     /* 减少上边距 */
}

/* 输入区域样式 */
.border-top {
    border-top: 1px solid #dee2e6 !important;
    background-color: #ffffff;
}

#messageInput {
    border-radius: 1.5rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    min-height: 44px; /* 确保输入框有最小高度 */
    font-size: 1rem;
}

#messageInput:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#sendButton {
    border-radius: 1.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
    min-height: 44px; /* 确保按钮有最小高度 */
    white-space: nowrap;
}

#sendButton:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

#sendButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 输入组样式 */
.input-group {
    display: flex;
    align-items: stretch;
    gap: 0.5rem;
}

.input-group .form-control {
    flex: 1;
    min-width: 0;
}

/* 状态指示器样式 */
#statusIndicator {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 会话列表样式 */
.list-group-item {
    border: 1px solid #e9ecef;
    margin-bottom: 0.5rem;
    border-radius: 0.5rem !important;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.list-group-item.active {
    background-color: #007bff;
    border-color: #007bff;
}

/* 按钮样式增强 */
.btn {
    transition: all 0.15s ease-in-out;
}

.btn:hover:not(:disabled) {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* 卡片样式 */
.card {
    transition: all 0.15s ease-in-out;
    border: 1px solid #e9ecef;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 新的三栏布局 */
.chat-container {
    height: 100%;
    max-height: 100%;
    overflow: hidden;
    display: flex;
    flex: 1;
    min-height: 0; /* 重要：允许flex子元素收缩 */
}

/* 左侧会话列表 */
.sessions-sidebar {
    width: 280px;
    height: 100%;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
}

/* 会话列表内容区域 */
#sessionsList, #sessionList {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 中间聊天区域 */
.chat-main {
    flex: 1;
    height: 100%;
    max-height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0; /* 确保flex子元素能正确收缩 */
    min-width: 0; /* 确保flex子元素能正确收缩 */
    position: relative;
}

/* 聊天头部固定 */
.chat-main .border-bottom {
    flex-shrink: 0;
}

/* 聊天消息区域可滚动 */
.chat-main .flex-grow-1 {
    flex: 1;
    min-height: 0;
    max-height: calc(100% - 140px); /* 减去头部和输入区域的高度 */
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
}

/* 输入区域固定在底部 */
.chat-main .border-top {
    flex-shrink: 0;
    background-color: white !important;
    border-top: 1px solid #dee2e6 !important;
    padding: 1rem !important;
    min-height: 80px; /* 确保输入区域有足够高度 */
    max-height: 120px; /* 防止过高 */
    position: relative;
    z-index: 100;
}

/* 右侧配置面板 */
.config-sidebar {
    width: 320px;
    height: 100%;
    background-color: #f8f9fa;
    border-left: 1px solid #dee2e6;
    overflow-y: auto;
    flex-shrink: 0;
    transition: margin-right 0.3s ease-in-out;
}

.config-sidebar.collapsed {
    margin-right: -320px;
}



/* 隐藏滚动条但保持功能 */
.sessions-sidebar::-webkit-scrollbar,
.config-sidebar::-webkit-scrollbar {
    width: 4px;
}

.sessions-sidebar::-webkit-scrollbar-track,
.config-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sessions-sidebar::-webkit-scrollbar-thumb,
.config-sidebar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.sessions-sidebar::-webkit-scrollbar-thumb:hover,
.config-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* 会话列表样式 */
.session-item {
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.session-item:hover {
    background-color: #e9ecef;
}

.session-item.active {
    background-color: #007bff;
    color: white;
}

.session-item.active:hover {
    background-color: #0056b3;
}

.session-title {
    font-weight: 500;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.session-preview {
    font-size: 0.875rem;
    opacity: 0.7;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.session-time {
    font-size: 0.75rem;
    opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .config-sidebar {
        width: 280px;
    }

    .config-sidebar.collapsed {
        margin-right: -280px;
    }
}

@media (max-width: 992px) {
    .sessions-sidebar {
        width: 240px;
    }

    .config-sidebar {
        width: 260px;
    }

    .config-sidebar.collapsed {
        margin-right: -260px;
    }
}

@media (max-width: 768px) {
    main.container-fluid {
        height: calc(100vh - 60px);
        height: calc(100dvh - 60px); /* 使用动态视口高度 */
        max-height: calc(100vh - 60px);
        max-height: calc(100dvh - 60px);
    }

    .chat-container {
        flex-direction: column;
        height: 100%;
    }

    .sessions-sidebar {
        width: 100%;
        height: 150px;
        border-right: none;
        border-bottom: 1px solid #dee2e6;
        flex-shrink: 0;
    }

    .chat-main {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
    }

    .chat-main .border-top {
        padding: 0.75rem !important;
        min-height: auto;
        flex-shrink: 0;
    }

    .config-sidebar {
        width: 100%;
        height: auto;
        max-height: 250px;
        border-left: none;
        border-top: 1px solid #dee2e6;
        margin-right: 0;
        flex-shrink: 0;
    }

    .config-sidebar.collapsed {
        margin-right: 0;
        height: 0;
        overflow: hidden;
    }

    .message-bubble {
        max-width: 90%;              /* 移动端增加宽度利用率 */
        padding: 0.5rem 0.7rem !important;  /* 移动端更紧凑的内边距 */
    }

    .message-content {
        font-size: 0.9rem;          /* 移动端稍微减小字体 */
        line-height: 1.35;          /* 移动端更紧凑的行高 */
    }

    .message-content ul,
    .message-content ol {
        margin-bottom: 0.03rem !important;   /* 移动端进一步减少列表间距 */
    }

    .message-item {
        margin-bottom: 0.6rem !important;   /* 移动端减少消息间距 */
    }

    /* 移动端输入框优化 */
    #messageInput {
        font-size: 16px; /* 防止iOS缩放 */
        min-height: 44px;
    }

    #sendButton {
        min-height: 44px;
        padding: 0.5rem 1rem;
    }

    .input-group {
        gap: 0.25rem;
    }
}

/* 极小屏幕优化 */
@media (max-width: 480px) {
    .sessions-sidebar {
        height: 120px;
    }

    .chat-main .border-top {
        padding: 0.5rem !important;
    }

    #messageInput {
        font-size: 16px;
        padding: 0.5rem 0.75rem;
    }

    #sendButton {
        padding: 0.5rem 0.75rem;
        min-width: 60px;
    }

    #sendButton .bi-send {
        font-size: 1.1rem;
    }

    .config-sidebar {
        max-height: 200px;
    }

    /* 极小屏幕的紧凑优化 */
    .message-bubble {
        max-width: 95% !important;   /* 极小屏幕最大化宽度利用 */
        padding: 0.4rem 0.6rem !important;  /* 更紧凑的内边距 */
    }

    .message-content {
        font-size: 0.85rem !important;      /* 更小的字体 */
        line-height: 1.3 !important;        /* 更紧凑的行高 */
    }

    .message-content ul,
    .message-content ol {
        margin-bottom: 0.02rem !important;  /* 极小屏幕最小列表间距 */
    }

    .message-content h1 { font-size: 1.1rem !important; }
    .message-content h2 { font-size: 1.05rem !important; }
    .message-content h3 { font-size: 1rem !important; }
    .message-content h4,
    .message-content h5,
    .message-content h6 { font-size: 0.9rem !important; }

    .message-item {
        margin-bottom: 0.5rem !important;   /* 极小屏幕最小间距 */
    }

    .message-time {
        font-size: 0.65rem !important;      /* 更小的时间戳 */
    }
}

/* 确保输入区域始终可见 */
.chat-main > .border-top {
    position: relative;
    z-index: 10;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white !important;
}

/* 修复可能的布局问题 */
.chat-main {
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 100%;
}

.chat-main .flex-grow-1 {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    padding-bottom: 0.5rem; /* 为输入区域留出空间 */
}

/* 防止输入框被虚拟键盘遮挡 */
@supports (-webkit-touch-callout: none) {
    /* iOS Safari */
    .chat-main .border-top {
        padding-bottom: env(safe-area-inset-bottom, 1rem) !important;
    }
}

/* Markdown 内容样式 - 紧凑优化 */
.message-content {
    line-height: 1.4;        /* 更紧凑的行高 */
}

.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
    margin-top: 0.6rem;      /* 减少标题上间距 */
    margin-bottom: 0.3rem;   /* 减少标题下间距 */
    font-weight: 600;
    line-height: 1.3;        /* 标题行高更紧凑 */
}

.message-content h1 { font-size: 1.3rem; }  /* 减小标题字体 */
.message-content h2 { font-size: 1.2rem; }
.message-content h3 { font-size: 1.1rem; }
.message-content h4 { font-size: 1.05rem; }
.message-content h5 { font-size: 1rem; }
.message-content h6 { font-size: 0.95rem; }

.message-content p {
    margin-bottom: 0.4rem;   /* 大幅减少段落间距 */
}

.message-content ul,
.message-content ol {
    margin-bottom: 0.05rem;  /* 进一步减少列表底部间距 */
    padding-left: 1.2rem;    /* 减少列表缩进 */
    margin-top: 0.05rem;     /* 进一步减少列表上间距 */
}

.message-content li {
    margin-bottom: 0.05rem;  /* 进一步减少列表项间距 */
    line-height: 1.25;       /* 列表项行高更紧凑 */
}

.message-content blockquote {
    border-left: 3px solid #dee2e6;  /* 减少边框宽度 */
    padding-left: 0.8rem;            /* 减少内边距 */
    margin: 0.4rem 0;                /* 减少外边距 */
    color: #6c757d;
    font-style: italic;
    line-height: 1.3;                /* 紧凑行高 */
}

.message-content code {
    background-color: #f8f9fa;
    padding: 0.1rem 0.2rem;          /* 减少内边距 */
    border-radius: 0.2rem;           /* 减少圆角 */
    font-size: 0.85em;               /* 稍微减小字体 */
    color: #e83e8c;
}

.message-content pre {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.3rem;           /* 减少圆角 */
    padding: 0.6rem 0.8rem;          /* 减少内边距 */
    margin: 0.4rem 0;                /* 减少外边距 */
    overflow-x: auto;
    position: relative;
    font-size: 0.85rem;              /* 减小代码字体 */
    line-height: 1.3;                /* 紧凑行高 */
}

.message-content pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
    font-size: 0.875rem;
}

.message-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 0.3rem 0;        /* 减少表格间距 */
    font-size: 0.875rem;
}

.message-content th,
.message-content td {
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    text-align: left;
}

.message-content th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.message-content a {
    color: #0d6efd;
    text-decoration: none;
}

.message-content a:hover {
    text-decoration: underline;
}

/* 代码复制按钮 */
.code-block-wrapper {
    position: relative;
}

.copy-code-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s;
}

.code-block-wrapper:hover .copy-code-btn {
    opacity: 1;
}

.copy-code-btn:hover {
    background: rgba(255, 255, 255, 0.95);
}

/* 用户消息中的代码样式调整 */
.user-message .message-content code {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
}

.user-message .message-content pre {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.user-message .message-content blockquote {
    border-left-color: rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.8);
}

/* 语音控制面板样式 */
.voice-control-panel {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    flex-shrink: 0;
    min-height: 50px;
}

.voice-status {
    display: flex;
    align-items: center;
}

.voice-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.voice-controls .btn {
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.voice-controls .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.voice-controls .btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.voice-controls .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* 语音状态指示器动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.voice-status .bi-volume-up {
    animation: pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 576px) {
    .voice-control-panel {
        padding: 0.5rem 1rem !important;
    }

    .voice-controls {
        gap: 0.25rem;
    }

    .voice-controls .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .voice-controls .btn .d-none {
        display: none !important;
    }

    .voice-status small {
        font-size: 0.75rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toast 样式增强 */
.toast {
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 表单控件焦点样式 */
.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 复选框和单选按钮样式 */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 错误状态样式 */
.is-invalid {
    border-color: #dc3545;
}

.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* 成功状态样式 */
.is-valid {
    border-color: #28a745;
}

.is-valid:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}


