/**
 * TTS Demo 聊天界面通用JavaScript函数
 */

// Toast 通知函数
function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    const toastBody = toast.querySelector('.toast-body');
    const toastHeader = toast.querySelector('.toast-header');
    const icon = toastHeader.querySelector('i');

    // 设置消息内容
    toastBody.textContent = message;

    // 根据类型设置图标和样式
    switch (type) {
        case 'success':
            icon.className = 'bi bi-check-circle text-success me-2';
            toast.className = 'toast border-success';
            break;
        case 'error':
            icon.className = 'bi bi-exclamation-triangle text-danger me-2';
            toast.className = 'toast border-danger';
            break;
        case 'warning':
            icon.className = 'bi bi-exclamation-triangle text-warning me-2';
            toast.className = 'toast border-warning';
            break;
        default:
            icon.className = 'bi bi-info-circle text-primary me-2';
            toast.className = 'toast border-primary';
    }

    // 显示Toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: type === 'error' ? 5000 : 3000
    });
    bsToast.show();
}

// 格式化时间
function formatTime(date) {
    if (typeof date === 'string') {
        date = new Date(date);
    }

    const now = new Date();
    const diff = now - date;

    // 小于1分钟
    if (diff < 60000) {
        return '刚刚';
    }

    // 小于1小时
    if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `${minutes}分钟前`;
    }

    // 小于1天
    if (diff < 86400000) {
        const hours = Math.floor(diff / 3600000);
        return `${hours}小时前`;
    }

    // 大于1天，显示具体日期
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');

    const currentYear = now.getFullYear();
    if (year === currentYear) {
        return `${month}-${day} ${hour}:${minute}`;
    } else {
        return `${year}-${month}-${day} ${hour}:${minute}`;
    }
}

// 复制文本到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showToast('已复制到剪贴板', 'success');
        return true;
    } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            showToast('已复制到剪贴板', 'success');
            return true;
        } catch (err) {
            showToast('复制失败', 'error');
            return false;
        } finally {
            document.body.removeChild(textArea);
        }
    }
}

// 验证表单字段
function validateField(field, rules) {
    const value = field.value.trim();
    const errors = [];

    // 必填验证
    if (rules.required && !value) {
        errors.push('此字段为必填项');
    }

    // 最小长度验证
    if (rules.minLength && value.length < rules.minLength) {
        errors.push(`最少需要${rules.minLength}个字符`);
    }

    // 最大长度验证
    if (rules.maxLength && value.length > rules.maxLength) {
        errors.push(`最多允许${rules.maxLength}个字符`);
    }

    // 正则表达式验证
    if (rules.pattern && !rules.pattern.test(value)) {
        errors.push(rules.patternMessage || '格式不正确');
    }

    // 自定义验证函数
    if (rules.validator && typeof rules.validator === 'function') {
        const customError = rules.validator(value);
        if (customError) {
            errors.push(customError);
        }
    }

    // 更新字段状态
    if (errors.length > 0) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');

        // 显示错误信息
        let feedback = field.parentNode.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentNode.appendChild(feedback);
        }
        feedback.textContent = errors[0];

        return false;
    } else {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');

        // 移除错误信息
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }

        return true;
    }
}

// 防抖函数
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 检查网络连接状态
function checkNetworkStatus() {
    return navigator.onLine;
}

// 监听网络状态变化
window.addEventListener('online', function() {
    showToast('网络连接已恢复', 'success');
});

window.addEventListener('offline', function() {
    showToast('网络连接已断开', 'warning');
});

// 本地存储工具函数
const storage = {
    // 设置项目
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (e) {
            console.error('存储失败:', e);
            return false;
        }
    },

    // 获取项目
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (e) {
            console.error('读取存储失败:', e);
            return defaultValue;
        }
    },

    // 删除项目
    remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (e) {
            console.error('删除存储失败:', e);
            return false;
        }
    },

    // 清空所有
    clear() {
        try {
            localStorage.clear();
            return true;
        } catch (e) {
            console.error('清空存储失败:', e);
            return false;
        }
    }
};

// HTTP 请求工具函数
const http = {
    // GET 请求
    async get(url, options = {}) {
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('GET请求失败:', error);
            throw error;
        }
    },

    // POST 请求
    async post(url, data = {}, options = {}) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                body: JSON.stringify(data),
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('POST请求失败:', error);
            throw error;
        }
    },

    // PUT 请求
    async put(url, data = {}, options = {}) {
        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                body: JSON.stringify(data),
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('PUT请求失败:', error);
            throw error;
        }
    },

    // DELETE 请求
    async delete(url, options = {}) {
        try {
            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('DELETE请求失败:', error);
            throw error;
        }
    }
};

// Markdown 渲染配置和函数
let markdownRenderer = null;

// 初始化 Markdown 渲染器
function initMarkdownRenderer() {
    if (typeof marked === 'undefined') {
        console.warn('Marked.js 未加载，将使用纯文本显示');
        return null;
    }

    // 配置 marked
    marked.setOptions({
        highlight: function(code, lang) {
            if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                try {
                    return hljs.highlight(code, { language: lang }).value;
                } catch (err) {
                    console.warn('代码高亮失败:', err);
                }
            }
            return code;
        },
        breaks: true,
        gfm: true,
        sanitize: false,
        smartLists: true,
        smartypants: false
    });

    // 自定义渲染器
    const renderer = new marked.Renderer();

    // 自定义代码块渲染，添加复制按钮
    renderer.code = function(code, language) {
        const validLang = language && hljs.getLanguage(language) ? language : '';
        const highlightedCode = validLang ? hljs.highlight(code, { language: validLang }).value : escapeHtml(code);

        return `
            <div class="code-block-wrapper">
                <pre><code class="hljs ${validLang}">${highlightedCode}</code></pre>
                <button class="copy-code-btn" onclick="copyCodeToClipboard(this)" title="复制代码">
                    <i class="bi bi-clipboard"></i>
                </button>
            </div>
        `;
    };

    // 自定义链接渲染，添加安全属性
    renderer.link = function(href, title, text) {
        const titleAttr = title ? ` title="${escapeHtml(title)}"` : '';
        return `<a href="${escapeHtml(href)}"${titleAttr} target="_blank" rel="noopener noreferrer">${text}</a>`;
    };

    marked.use({ renderer });
    return marked;
}

// 渲染 Markdown 文本
function renderMarkdown(text) {
    if (!markdownRenderer) {
        markdownRenderer = initMarkdownRenderer();
    }

    // 预处理：将双换行符替换为单换行符
    let processedText = text.replace(/\n\n+/g, '\n');

    if (!markdownRenderer) {
        // 如果 marked 不可用，返回转义的 HTML
        return escapeHtml(processedText).replace(/\n/g, '<br>');
    }

    try {
        return markdownRenderer.parse(processedText);
    } catch (error) {
        console.error('Markdown 渲染失败:', error);
        return escapeHtml(processedText).replace(/\n/g, '<br>');
    }
}

// 复制代码块内容
function copyCodeToClipboard(button) {
    const codeBlock = button.parentNode.querySelector('code');
    const code = codeBlock.textContent || codeBlock.innerText;

    copyToClipboard(code).then(() => {
        // 临时改变按钮图标
        const icon = button.querySelector('i');
        const originalClass = icon.className;
        icon.className = 'bi bi-check';

        setTimeout(() => {
            icon.className = originalClass;
        }, 2000);
    });
}

// HTML 转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化 Markdown 渲染器
    initMarkdownRenderer();

    // 初始化所有工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化所有弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// 导出到全局作用域
window.showToast = showToast;
window.formatTime = formatTime;
window.copyToClipboard = copyToClipboard;
window.validateField = validateField;
window.debounce = debounce;
window.throttle = throttle;
window.checkNetworkStatus = checkNetworkStatus;
window.storage = storage;
window.http = http;
window.renderMarkdown = renderMarkdown;
window.escapeHtml = escapeHtml;
window.copyCodeToClipboard = copyCodeToClipboard;
