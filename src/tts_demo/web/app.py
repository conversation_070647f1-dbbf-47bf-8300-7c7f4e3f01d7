"""
Web聊天界面应用

基于FastAPI和WebSocket的实时聊天界面，支持：
- OpenAI和Langflow两种LLM源
- 实时流式对话
- TTS语音合成
- 配置管理
- 会话历史
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
import uvicorn

from ..config import get_app_config, WebConfig
from ..config.constants import (
    DEFAULT_FIRST_CHUNK_BREAK_MODE,
    DEFAULT_LANGFLOW_URL,
    DEFAULT_LLM_MODEL,
    DEFAULT_TTS_VOICE,
    DEFAULT_TTS_RATE,
    DEFAULT_TTS_VOLUME,
    DEFAULT_FIRST_CHUNK_SIZE,
    DEFAULT_MIN_SENTENCE_SIZE,
    DEFAULT_MAX_CHUNK_SIZE
)
from ..llm import OpenAIClient, LangflowClient
from ..speaker import StreamSpeaker

# 配置日志
logger = logging.getLogger("WebChat")


class DateTimeEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime对象"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class ChatMessage(BaseModel):
    """聊天消息模型"""
    id: str
    role: str  # "user" 或 "assistant"
    content: str
    timestamp: datetime
    session_id: str


class ChatConfig(BaseModel):
    """聊天配置模型"""
    # LLM配置
    use_langflow: bool = False
    model: str = DEFAULT_LLM_MODEL
    api_key: Optional[str] = None
    base_url: Optional[str] = None

    # Langflow配置
    langflow_base_url: str = DEFAULT_LANGFLOW_URL
    langflow_api_key: Optional[str] = None
    langflow_chat_flow_id: Optional[str] = None
    langflow_operator_flow_ids: Optional[List[str]] = None
    langflow_session_id: Optional[str] = None
    langflow_tweaks: Optional[Dict] = None

    # TTS配置
    tts_enabled: bool = True  # TTS启用状态
    voice: str = DEFAULT_TTS_VOICE
    rate: str = DEFAULT_TTS_RATE
    volume: str = DEFAULT_TTS_VOLUME
    first_chunk_size: int = DEFAULT_FIRST_CHUNK_SIZE
    min_sentence_size: int = DEFAULT_MIN_SENTENCE_SIZE
    max_chunk_size: int = DEFAULT_MAX_CHUNK_SIZE
    first_chunk_break_mode: str = DEFAULT_FIRST_CHUNK_BREAK_MODE

    # 队列配置
    queue_size: int = 10
    max_queue_size: int = 50

    # 缓存配置
    enable_cache: bool = True
    cache_size: int = 100
    cache_memory_mb: float = 100.0

    # 高级配置
    custom_words: Optional[str] = None
    debug: bool = False
    stream: bool = True  # 全局流式响应配置


class ChatManager:
    """聊天管理器"""

    def __init__(self):
        self.sessions: Dict[str, List[ChatMessage]] = {}
        self.active_connections: Dict[str, WebSocket] = {}
        self.configs: Dict[str, ChatConfig] = {}
        self.speakers: Dict[str, StreamSpeaker] = {}  # TTS处理器
        self.active_tasks: Dict[str, asyncio.Task] = {}  # 活动的流式处理任务
        self.tts_tasks: Dict[str, asyncio.Task] = {}  # 活动的TTS处理任务

    def create_session(self) -> str:
        """创建新的聊天会话"""
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = []
        self.configs[session_id] = ChatConfig()
        return session_id

    def get_session_messages(self, session_id: str) -> List[ChatMessage]:
        """获取会话消息"""
        return self.sessions.get(session_id, [])

    def add_message(self, session_id: str, role: str, content: str) -> ChatMessage:
        """添加消息到会话"""
        message = ChatMessage(
            id=str(uuid.uuid4()),
            role=role,
            content=content,
            timestamp=datetime.now(),
            session_id=session_id
        )

        if session_id not in self.sessions:
            self.sessions[session_id] = []

        self.sessions[session_id].append(message)
        return message

    def get_config(self, session_id: str) -> ChatConfig:
        """获取会话配置"""
        if session_id not in self.configs:
            self.configs[session_id] = ChatConfig()
        return self.configs[session_id]

    def update_config(self, session_id: str, config: ChatConfig):
        """更新会话配置"""
        self.configs[session_id] = config

        # 如果有现有的TTS处理器，需要重新创建以应用新配置
        if session_id in self.speakers:
            asyncio.create_task(self._recreate_speaker(session_id))

    async def _recreate_speaker(self, session_id: str):
        """重新创建TTS处理器以应用新配置"""
        if session_id in self.speakers:
            try:
                # 停止旧的处理器
                await self.speakers[session_id].stop()
                del self.speakers[session_id]
                logger.info(f"已停止旧的TTS处理器: {session_id}")

                # 新的处理器将在下次需要时创建
            except Exception as e:
                logger.error(f"重新创建TTS处理器失败: {e}")

    async def connect_websocket(self, websocket: WebSocket, session_id: str):
        """连接WebSocket"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        logger.info(f"WebSocket连接建立: {session_id}")

    def disconnect_websocket(self, session_id: str):
        """断开WebSocket连接"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]

        # 取消活动的流式处理任务
        if session_id in self.active_tasks:
            task = self.active_tasks[session_id]
            if not task.done():
                task.cancel()
            del self.active_tasks[session_id]

        # 取消活动的TTS处理任务
        if session_id in self.tts_tasks:
            task = self.tts_tasks[session_id]
            if not task.done():
                task.cancel()
            del self.tts_tasks[session_id]

        # 停止TTS处理器
        if session_id in self.speakers:
            asyncio.create_task(self._stop_speaker(session_id))

        logger.info(f"WebSocket连接断开: {session_id}")

    async def _stop_speaker(self, session_id: str):
        """停止TTS处理器"""
        if session_id in self.speakers:
            try:
                await self.speakers[session_id].stop()
                del self.speakers[session_id]
                logger.info(f"TTS处理器已停止: {session_id}")
            except Exception as e:
                logger.error(f"停止TTS处理器失败: {e}")

    async def get_or_create_speaker(self, session_id: str) -> StreamSpeaker:
        """获取或创建TTS处理器"""
        if session_id not in self.speakers:
            config = self.get_config(session_id)

            # 创建现代化TTS处理器
            speaker = StreamSpeaker(
                voice=config.voice,
                rate=config.rate,
                volume=config.volume,
                first_chunk_size=config.first_chunk_size,
                min_sentence_size=config.min_sentence_size,
                max_chunk_size=config.max_chunk_size,
                first_chunk_break_mode=config.first_chunk_break_mode,
                on_text_chunk=lambda text: logger.debug(f"TTS朗读: {text[:20]}..."),
                queue_size=config.queue_size,
                max_queue_size=config.max_queue_size,
                enable_cache=config.enable_cache,
                cache_size=config.cache_size,
                cache_memory_mb=config.cache_memory_mb,
                debug_mode=config.debug
            )

            # 启动TTS处理器
            await speaker.start()
            self.speakers[session_id] = speaker
            logger.info(f"TTS处理器已创建: {session_id}")

        return self.speakers[session_id]

    async def send_message(self, session_id: str, message: dict):
        """发送消息到WebSocket"""
        if session_id in self.active_connections:
            try:
                await self.active_connections[session_id].send_text(
                    json.dumps(message, ensure_ascii=False, cls=DateTimeEncoder)
                )
            except Exception as e:
                logger.error(f"发送消息失败: {e}")
                self.disconnect_websocket(session_id)


def create_app(config: Optional[WebConfig] = None) -> FastAPI:
    """创建FastAPI应用"""
    if config is None:
        app_config = get_app_config()
        config = app_config.web

    # 创建FastAPI应用
    app = FastAPI(
        title="TTS Demo 聊天界面",
        description="支持OpenAI和Langflow的实时聊天界面"
    )

    # 创建聊天管理器
    chat_manager = ChatManager()

    # 设置静态文件和模板目录
    static_dir = Path(__file__).parent / "static"
    templates_dir = Path(__file__).parent / "templates"

    # 确保目录存在
    static_dir.mkdir(parents=True, exist_ok=True)
    templates_dir.mkdir(parents=True, exist_ok=True)

    # 配置静态文件和模板
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    templates = Jinja2Templates(directory=str(templates_dir))

    # 路由定义
    @app.get("/", response_class=HTMLResponse)
    async def index(request: Request):
        """主页"""
        return templates.TemplateResponse("index.html", {
            "request": request
        })

    @app.get("/chat/{session_id}", response_class=HTMLResponse)
    async def chat_page(request: Request, session_id: str):
        """聊天页面"""
        if session_id not in chat_manager.sessions:
            chat_manager.sessions[session_id] = []
            chat_manager.configs[session_id] = ChatConfig()

        return templates.TemplateResponse("chat.html", {
            "request": request,
            "session_id": session_id,
            "messages": chat_manager.get_session_messages(session_id),
            "config": chat_manager.get_config(session_id)
        })

    @app.get("/api/sessions")
    async def get_sessions():
        """获取所有会话"""
        session_data = []

        # 先收集所有会话数据
        for session_id, messages in chat_manager.sessions.items():
            last_message = messages[-1] if messages else None
            timestamp = last_message.timestamp if last_message else datetime.now()

            session_info = {
                "id": session_id,
                "last_message": last_message.content if last_message else "新会话",
                "timestamp": timestamp.isoformat(),
                "message_count": len(messages),
                "_sort_timestamp": timestamp
            }
            session_data.append(session_info)

        # 按时间排序
        session_data.sort(key=lambda x: x["_sort_timestamp"], reverse=True)

        # 移除排序用的字段
        for session in session_data:
            del session["_sort_timestamp"]

        return session_data

    @app.get("/api/sessions/{session_id}/messages")
    async def get_session_messages_api(session_id: str):
        """获取会话消息"""
        messages = chat_manager.get_session_messages(session_id)
        return [msg.model_dump(mode='json') for msg in messages]

    @app.get("/api/sessions/{session_id}/config")
    async def get_session_config_api(session_id: str):
        """获取会话配置"""
        config = chat_manager.get_config(session_id)
        return config.model_dump(mode='json')

    @app.post("/api/sessions/{session_id}/config")
    async def update_session_config_api(session_id: str, config: ChatConfig):
        """更新会话配置"""
        chat_manager.update_config(session_id, config)
        return {"status": "success"}

    @app.post("/api/sessions/{session_id}/test-tts")
    async def test_tts_config_api(session_id: str, request: dict):
        """测试TTS配置"""
        try:
            text = request.get("text", "这是一个语音测试。")

            # 获取或创建TTS处理器
            speaker = await chat_manager.get_or_create_speaker(session_id)

            # 创建一个简单的文本生成器
            async def text_generator():
                yield text

            # 使用TTS处理器播放测试文本
            await speaker.process_stream(text_generator(), lambda x: x)

            return {"status": "success", "message": "测试语音播放完成"}
        except Exception as e:
            logger.error(f"TTS测试失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.post("/api/sessions/{session_id}/tts/stop")
    async def stop_tts_api(session_id: str):
        """停止TTS播放并禁用当前会话的TTS功能"""
        try:
            # 禁用当前会话的TTS功能
            config = chat_manager.get_config(session_id)
            config.tts_enabled = False
            chat_manager.update_config(session_id, config)
            logger.info(f"会话 {session_id} 的TTS功能已禁用")

            # 取消TTS处理任务
            if session_id in chat_manager.tts_tasks:
                task = chat_manager.tts_tasks[session_id]
                if not task.done():
                    task.cancel()
                    logger.info(f"已取消会话 {session_id} 的TTS处理任务")
                del chat_manager.tts_tasks[session_id]

            # 停止TTS处理器
            if session_id in chat_manager.speakers:
                speaker = chat_manager.speakers[session_id]
                await speaker.stop()
                # 重新启动以准备下次使用
                await speaker.start()
                return {"status": "success", "message": "语音播放已停止，新内容将不再朗读"}
            else:
                return {"status": "success", "message": "TTS功能已禁用"}
        except Exception as e:
            logger.error(f"停止TTS失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.post("/api/sessions/{session_id}/tts/start")
    async def start_tts_api(session_id: str):
        """启用当前会话的TTS功能"""
        try:
            # 启用当前会话的TTS功能
            config = chat_manager.get_config(session_id)
            config.tts_enabled = True
            chat_manager.update_config(session_id, config)
            logger.info(f"会话 {session_id} 的TTS功能已启用")

            return {"status": "success", "message": "TTS功能已启用，新内容将开始朗读"}
        except Exception as e:
            logger.error(f"启用TTS失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/sessions/{session_id}/tts/status")
    async def get_tts_status_api(session_id: str):
        """获取TTS状态"""
        try:
            config = chat_manager.get_config(session_id)

            if session_id in chat_manager.speakers:
                speaker = chat_manager.speakers[session_id]
                return {
                    "status": "success",
                    "data": {
                        "tts_enabled": config.tts_enabled,
                        "is_running": speaker.is_running,
                        "playback_state": speaker.playback_state.value,
                        "stats": speaker.get_stats()
                    }
                }
            else:
                return {
                    "status": "success",
                    "data": {
                        "tts_enabled": config.tts_enabled,
                        "is_running": False,
                        "playback_state": "stopped",
                        "stats": {}
                    }
                }
        except Exception as e:
            logger.error(f"获取TTS状态失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.delete("/api/sessions/{session_id}")
    async def delete_session_api(session_id: str):
        """删除会话"""
        if session_id in chat_manager.sessions:
            del chat_manager.sessions[session_id]
        if session_id in chat_manager.configs:
            del chat_manager.configs[session_id]
        if session_id in chat_manager.speakers:
            del chat_manager.speakers[session_id]

        chat_manager.disconnect_websocket(session_id)
        return {"status": "success"}

    @app.websocket("/ws/{session_id}")
    async def websocket_endpoint(websocket: WebSocket, session_id: str):
        """WebSocket端点"""
        await chat_manager.connect_websocket(websocket, session_id)

        try:
            while True:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)

                if message_data["type"] == "chat":
                    await handle_chat_message(chat_manager, session_id, message_data["content"])

        except WebSocketDisconnect:
            chat_manager.disconnect_websocket(session_id)
        except Exception as e:
            logger.error(f"WebSocket错误: {e}")
            chat_manager.disconnect_websocket(session_id)

    return app


async def handle_chat_message(chat_manager: ChatManager, session_id: str, user_message: str):
    """处理聊天消息"""
    try:
        # 添加用户消息
        user_msg = chat_manager.add_message(session_id, "user", user_message)
        await chat_manager.send_message(session_id, {
            "type": "message",
            "message": user_msg.model_dump(mode='json')
        })

        # 获取配置
        config = chat_manager.get_config(session_id)

        # 准备消息历史
        messages = []
        for msg in chat_manager.get_session_messages(session_id):
            messages.append({
                "role": msg.role,
                "content": msg.content
            })

        # 发送开始生成消息
        await chat_manager.send_message(session_id, {
            "type": "start"
        })

        # 创建助手消息
        assistant_msg = chat_manager.add_message(session_id, "assistant", "")

        # 初始化LLM客户端
        if config.use_langflow:
            client = LangflowClient(
                base_url=config.langflow_base_url,
                api_key=config.langflow_api_key,
                chat_flow_id=config.langflow_chat_flow_id,
                operator_flow_ids=config.langflow_operator_flow_ids
            )
        else:
            client = OpenAIClient(
                api_key=config.api_key,
                base_url=config.base_url,
                model=config.model
            )

        # 获取或创建TTS处理器
        speaker = await chat_manager.get_or_create_speaker(session_id)

        # 获取流式响应
        if config.use_langflow:
            stream = client.chat_completion(
                messages,
                stream=config.stream,
                session_id=config.langflow_session_id,
                tweaks=config.langflow_tweaks
            )
        else:
            stream = client.chat_completion(messages, stream=config.stream)

        # 创建共享的流处理器
        chunks_for_tts = []  # 存储用于TTS的chunks
        tts_enabled = config.tts_enabled  # 从配置获取TTS启用状态

        async def process_shared_stream():
            try:
                full_response = ""
                async for chunk in stream:
                    content = client.extract_content(chunk)
                    if content:
                        full_response += content

                        # 发送流式内容到前端
                        await chat_manager.send_message(session_id, {
                            "type": "token",
                            "content": content,
                            "message_id": assistant_msg.id
                        })

                        # 如果TTS启用，保存chunk用于TTS处理
                        if tts_enabled and session_id in chat_manager.tts_tasks:
                            chunks_for_tts.append(chunk)

                # 更新助手消息内容
                assistant_msg.content = full_response

                # 发送生成完成消息
                await chat_manager.send_message(session_id, {
                    "type": "end",
                    "message": assistant_msg.model_dump(mode='json')
                })

                return full_response
            except asyncio.CancelledError:
                logger.info(f"会话 {session_id} 的文本流式处理被取消")
                # 发送取消消息
                await chat_manager.send_message(session_id, {
                    "type": "end",
                    "message": assistant_msg.model_dump(mode='json')
                })
                raise
            finally:
                # 清理任务跟踪
                if session_id in chat_manager.active_tasks:
                    del chat_manager.active_tasks[session_id]

        # 创建TTS处理任务
        async def tts_stream_task():
            try:
                # 创建一个异步生成器来处理保存的chunks
                async def tts_chunk_generator():
                    chunk_index = 0
                    while True:
                        # 等待新的chunk或者流结束
                        while chunk_index >= len(chunks_for_tts):
                            if session_id not in chat_manager.active_tasks:
                                # 主流已结束
                                return
                            await asyncio.sleep(0.01)  # 短暂等待

                        # 检查TTS是否被停止
                        if session_id not in chat_manager.tts_tasks:
                            logger.info("TTS任务被停止，退出TTS处理")
                            return

                        yield chunks_for_tts[chunk_index]
                        chunk_index += 1

                # 使用TTS处理器处理流式输出
                await speaker.process_stream(tts_chunk_generator(), client.extract_content)
            except asyncio.CancelledError:
                logger.info(f"会话 {session_id} 的TTS处理被取消")
                raise
            finally:
                # 清理TTS任务跟踪
                if session_id in chat_manager.tts_tasks:
                    del chat_manager.tts_tasks[session_id]

        # 启动文本流式输出任务
        text_task = asyncio.create_task(process_shared_stream())
        chat_manager.active_tasks[session_id] = text_task

        # 只有在TTS启用时才启动TTS处理任务
        if tts_enabled:
            tts_task = asyncio.create_task(tts_stream_task())
            chat_manager.tts_tasks[session_id] = tts_task
            logger.info(f"会话 {session_id}: TTS功能已启用，启动TTS处理任务")
        else:
            logger.info(f"会话 {session_id}: TTS功能已禁用，跳过TTS处理")

        # 等待文本流式输出完成（TTS可以独立运行）
        await text_task

    except Exception as e:
        logger.error(f"处理聊天消息错误: {e}")
        await chat_manager.send_message(session_id, {
            "type": "error",
            "message": str(e)
        })


# 创建应用实例
app = create_app()

if __name__ == "__main__":
    # 启动应用
    print("启动Web界面...")
    print("访问地址: http://0.0.0.0:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
