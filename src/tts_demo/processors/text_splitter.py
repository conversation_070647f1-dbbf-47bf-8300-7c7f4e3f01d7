"""
智能文本分割器

从原始StreamSpeaker中提取的文本分割逻辑，进行模块化重构。
"""

import logging
from dataclasses import dataclass
from typing import List, Tuple, Optional

from ..core.models import BreakType
from ..config.models import FirstChunkBreakMode
from ..core.exceptions import TextProcessingError, ErrorCodes
from .text_processor import OptimizedTextProcessor

logger = logging.getLogger(__name__)


@dataclass
class TextSplitterConfig:
    """文本分割器配置"""
    sentence_ends: List[str]
    clause_breaks: List[str]
    other_breaks: List[str]
    first_chunk_size: int = 5
    min_sentence_size: int = 20
    max_chunk_size: int = 500


class SmartTextSplitter:
    """智能文本分割器"""
    
    def __init__(self, text_processor: OptimizedTextProcessor, config: TextSplitterConfig):
        self.text_processor = text_processor
        self.config = config
        
        # 预计算断句字符集合，提高查找效率
        self.sentence_end_set = set(config.sentence_ends)
        self.clause_break_set = set(config.clause_breaks)
        self.other_break_set = set(config.other_breaks)
        self.all_break_set = self.sentence_end_set | self.clause_break_set | self.other_break_set
        
        logger.debug(f"文本分割器初始化完成，断句字符集合大小: {len(self.all_break_set)}")
    
    def find_first_chunk_split(self, text: str, chunk_size: int, 
                              break_mode: FirstChunkBreakMode) -> Tuple[int, bool]:
        """查找首块分割位置
        
        Args:
            text: 要分割的文本
            chunk_size: 块大小
            break_mode: 断句模式
            
        Returns:
            (分割位置, 是否找到)
        """
        if not text:
            return 0, False
        
        try:
            if break_mode == FirstChunkBreakMode.PUNCTUATION:
                # 标点符号模式：查找任意标点符号
                all_breaks = self.config.sentence_ends + self.config.clause_breaks + self.config.other_breaks
                positions = self.text_processor.find_break_positions(text, all_breaks)
                if positions:
                    return min(positions), True
                return 0, False
            else:
                # 分词模式：使用jieba分词
                split_pos = self.text_processor.segment_text(text, chunk_size)
                return split_pos, split_pos > 0
        except Exception as e:
            raise TextProcessingError(
                f"首块分割失败: {str(e)}",
                text=text[:100] + "..." if len(text) > 100 else text,
                error_code=ErrorCodes.TEXT_BREAK_POSITION_FAILED
            )
    
    def find_sentence_split(self, text: str, min_size: int) -> Tuple[int, Optional[BreakType], bool]:
        """查找句子分割位置
        
        Args:
            text: 要分割的文本
            min_size: 最小大小
            
        Returns:
            (分割位置, 断句类型, 是否找到)
        """
        if len(text) < min_size:
            return 0, None, False
        
        try:
            # 1. 优先查找句子结束标记
            sentence_positions = self.text_processor.find_break_positions(text, self.config.sentence_ends)
            if sentence_positions:
                return min(sentence_positions), BreakType.SENTENCE, True
            
            # 2. 查找从句断句点
            clause_positions = self.text_processor.find_break_positions(text, self.config.clause_breaks)
            if clause_positions:
                return min(clause_positions), BreakType.CLAUSE, True
            
            return 0, None, False
        except Exception as e:
            raise TextProcessingError(
                f"句子分割失败: {str(e)}",
                text=text[:100] + "..." if len(text) > 100 else text,
                error_code=ErrorCodes.TEXT_BREAK_POSITION_FAILED
            )
    
    def find_forced_split(self, text: str, max_size: int) -> Tuple[int, bool]:
        """查找强制分割位置
        
        Args:
            text: 要分割的文本
            max_size: 最大大小
            
        Returns:
            (分割位置, 是否找到)
        """
        if len(text) < max_size:
            return 0, False
        
        try:
            # 在最大长度范围内查找其他断句点
            search_text = text[:max_size]
            positions = self.text_processor.find_break_positions(search_text, self.config.other_breaks)
            
            if positions:
                # 使用最靠后的分割点
                return max(positions), True
            else:
                # 如果没有找到合适的分割点，使用最大块大小的一半
                return max_size // 2, True
        except Exception as e:
            raise TextProcessingError(
                f"强制分割失败: {str(e)}",
                text=text[:100] + "..." if len(text) > 100 else text,
                error_code=ErrorCodes.TEXT_BREAK_POSITION_FAILED
            )
    
    def split_text_smart(self, text: str, is_first_chunk: bool = False, 
                        break_mode: FirstChunkBreakMode = FirstChunkBreakMode.PUNCTUATION) -> Tuple[str, str, BreakType]:
        """智能分割文本
        
        Args:
            text: 要分割的文本
            is_first_chunk: 是否为首块
            break_mode: 断句模式（仅对首块有效）
            
        Returns:
            (分割的文本, 剩余文本, 断句类型)
        """
        if not text:
            return "", "", BreakType.FORCED
        
        try:
            if is_first_chunk and len(text) >= self.config.first_chunk_size:
                # 处理首块
                split_pos, found = self.find_first_chunk_split(text, self.config.first_chunk_size, break_mode)
                if found:
                    split_pos = min(split_pos, len(text))
                    split_pos = max(split_pos, 1)
                    return text[:split_pos], text[split_pos:], BreakType.FIRST_CHUNK
            
            # 处理后续块
            # 1. 尝试按句子分割
            split_pos, break_type, found = self.find_sentence_split(text, self.config.min_sentence_size)
            if found:
                return text[:split_pos], text[split_pos:], break_type
            
            # 2. 如果文本过长，强制分割
            if len(text) >= self.config.max_chunk_size:
                split_pos, found = self.find_forced_split(text, self.config.max_chunk_size)
                if found:
                    split_pos = min(split_pos, len(text))
                    return text[:split_pos], text[split_pos:], BreakType.FORCED
            
            # 3. 如果都不满足条件，返回空分割
            return "", text, BreakType.FORCED
        except Exception as e:
            raise TextProcessingError(
                f"智能分割失败: {str(e)}",
                text=text[:100] + "..." if len(text) > 100 else text,
                error_code=ErrorCodes.TEXT_BREAK_POSITION_FAILED
            )
    
    def get_stats(self) -> dict:
        """获取分割器统计信息"""
        return {
            "config": {
                "sentence_ends_count": len(self.config.sentence_ends),
                "clause_breaks_count": len(self.config.clause_breaks),
                "other_breaks_count": len(self.config.other_breaks),
                "first_chunk_size": self.config.first_chunk_size,
                "min_sentence_size": self.config.min_sentence_size,
                "max_chunk_size": self.config.max_chunk_size
            },
            "break_char_sets": {
                "sentence_end_set_size": len(self.sentence_end_set),
                "clause_break_set_size": len(self.clause_break_set),
                "other_break_set_size": len(self.other_break_set),
                "all_break_set_size": len(self.all_break_set)
            }
        }
