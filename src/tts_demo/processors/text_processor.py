"""
优化的文本处理器

从原始StreamSpeaker中提取的文本处理逻辑，进行模块化重构。
"""

import re
import logging
from functools import lru_cache
from typing import List, Tuple

# import jieba  # 暂时注释掉，因为jieba有问题

from ..core.interfaces import TextProcessor
from ..core.exceptions import TextProcessingError, ErrorCodes

logger = logging.getLogger(__name__)


class OptimizedTextProcessor(TextProcessor):
    """优化的文本处理器实现"""

    def __init__(self, debug_mode: bool = False, cache_size: int = 500):
        self.debug_mode = debug_mode
        self.cache_size = cache_size
        self._init_regex_patterns()

        # 设置LRU缓存大小
        self.segment_text_cached = lru_cache(maxsize=cache_size)(self._segment_text_impl)

    def _init_regex_patterns(self):
        """预编译正则表达式模式"""
        try:
            # 文本过滤相关的正则表达式
            self.code_block_pattern = re.compile(r'```[\s\S]*?```')
            self.markdown_link_pattern = re.compile(r'\[([^\]]+)\]\([^)]+\)')
            self.html_tag_pattern = re.compile(r'<[^>]*>')
            self.valid_chars_pattern = re.compile(r'[^\u4e00-\u9fa5a-zA-Z0-9。，？！、""''（）(),.?! \s]')
            self.whitespace_pattern = re.compile(r'\s+')

            # 标点符号规范化
            self.comma_pattern = re.compile(r'[,.。，]{2,}')
            self.exclamation_pattern = re.compile(r'[!！]{2,}')
            self.question_pattern = re.compile(r'[?？]{2,}')

            logger.debug("正则表达式模式初始化完成")
        except Exception as e:
            raise TextProcessingError(
                "正则表达式模式初始化失败",
                error_code=ErrorCodes.TEXT_FILTER_FAILED,
                context={"error": str(e)}
            )

    def filter_content(self, text: str) -> str:
        """过滤文本内容，使用预编译的正则表达式"""
        if not text:
            return ""

        try:
            # 移除代码块
            text = self.code_block_pattern.sub('', text)
            # 移除Markdown链接，只保留链接文本
            text = self.markdown_link_pattern.sub(r'\1', text)
            # 移除HTML标签
            text = self.html_tag_pattern.sub('', text)
            # 只保留中英文字符、数字和基本标点
            text = self.valid_chars_pattern.sub('', text)
            # 移除多余空白字符
            text = self.whitespace_pattern.sub(' ', text)
            # 替换连续的标点符号为单个
            text = self.comma_pattern.sub('，', text)
            text = self.exclamation_pattern.sub('！', text)
            text = self.question_pattern.sub('？', text)

            return text.strip()
        except Exception as e:
            raise TextProcessingError(
                f"文本过滤失败: {str(e)}",
                text=text[:100] + "..." if len(text) > 100 else text,
                error_code=ErrorCodes.TEXT_FILTER_FAILED
            )

    def segment_text(self, text: str, max_words: int) -> int:
        """分词处理，使用缓存优化"""
        try:
            split_pos, words = self.segment_text_cached(text, max_words)

            if self.debug_mode:
                if len(words) <= max_words:
                    logger.debug(f"分词数量({len(words)})不超过最大词数({max_words})，使用全部文本")
                    logger.debug(f"完整分词结果: {' | '.join(words)}")
                else:
                    selected_words = words[:max_words]
                    logger.debug(f"分词数量({len(words)})超过最大词数({max_words})，截取前{max_words}个词: {' | '.join(selected_words)}")

            return split_pos
        except Exception as e:
            raise TextProcessingError(
                f"文本分词失败: {str(e)}",
                text=text[:100] + "..." if len(text) > 100 else text,
                error_code=ErrorCodes.TEXT_SEGMENTATION_FAILED
            )

    def _segment_text_impl(self, text: str, max_words: int) -> Tuple[int, List[str]]:
        """实际的分词实现（被LRU缓存装饰）"""
        if not text:
            return 0, []

        # 使用简单分词（暂时替代jieba）
        # words = list(jieba.cut(text))
        words = list(text)  # 简单的字符级分词

        # 限制最大词数
        if len(words) <= max_words:
            return len(text), words

        # 计算前max_words个词的总长度
        selected_words = words[:max_words]
        total_length = sum(len(word) for word in selected_words)

        return total_length, selected_words

    def find_break_positions(self, text: str, break_chars: List[str]) -> List[int]:
        """查找断句位置，使用优化算法"""
        if not text or not break_chars:
            return []

        try:
            positions = []
            # 使用集合提高查找效率
            break_char_set = set(break_chars)

            # 一次遍历找到所有断句位置
            for i, char in enumerate(text):
                if char in break_char_set:
                    positions.append(i + 1)  # 包含断句字符

            return positions
        except Exception as e:
            raise TextProcessingError(
                f"断句位置查找失败: {str(e)}",
                text=text[:100] + "..." if len(text) > 100 else text,
                error_code=ErrorCodes.TEXT_BREAK_POSITION_FAILED
            )

    def clear_cache(self):
        """清理缓存"""
        try:
            self.segment_text_cached.cache_clear()
            logger.debug("文本处理器缓存已清理")
        except Exception as e:
            logger.warning(f"清理缓存失败: {e}")

    def get_cache_info(self) -> dict:
        """获取缓存信息"""
        cache_info = self.segment_text_cached.cache_info()
        return {
            "hits": cache_info.hits,
            "misses": cache_info.misses,
            "maxsize": cache_info.maxsize,
            "currsize": cache_info.currsize,
            "hit_rate": cache_info.hits / (cache_info.hits + cache_info.misses) if (cache_info.hits + cache_info.misses) > 0 else 0.0
        }

    def get_stats(self) -> dict:
        """获取处理器统计信息"""
        return {
            "debug_mode": self.debug_mode,
            "cache_size": self.cache_size,
            "cache_info": self.get_cache_info()
        }
