import os
import time
import asyncio
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Callable, Union
from dotenv import load_dotenv

# 配置日志
logger = logging.getLogger("OpenAIClient")

# 加载环境变量
load_dotenv()

# 尝试导入OpenAI库，如果不存在则提供安装提示
try:
    from openai import AsyncOpenAI
    from openai.types.chat import ChatCompletionChunk
except ImportError:
    raise ImportError(
        "请安装OpenAI Python库: uv add openai\n"
        "或者更新项目依赖: uv sync"
    )

class OpenAIClient:
    """OpenAI API客户端，支持流式输出"""

    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model: Optional[str] = None
    ):
        """
        初始化OpenAI客户端

        参数:
            api_key: OpenAI API密钥，如果为None则从环境变量获取
            base_url: OpenAI API基础URL，如果为None则从环境变量获取
            model: 使用的模型名称，如果为None则从环境变量获取
        """
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY")
        self.base_url = base_url or os.environ.get("OPENAI_API_BASE")
        self.model = model or os.environ.get("DEFAULT_LLM_MODEL", "deepseek-chat")

        if not self.api_key:
            raise ValueError(
                "未提供OpenAI API密钥。请通过参数提供、设置OPENAI_API_KEY环境变量或在.env文件中配置。"
            )

        self.client = AsyncOpenAI(api_key=self.api_key, base_url=self.base_url)

    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        stream: bool = True,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        on_first_token: Optional[Callable] = None,
        **kwargs
    ) -> AsyncGenerator[Union[ChatCompletionChunk, Any], None]:
        """
        创建聊天完成请求

        参数:
            messages: 消息列表，格式为[{"role": "user", "content": "你好"}]
            stream: 是否使用流式输出
            temperature: 温度参数，控制随机性
            max_tokens: 最大生成token数
            on_first_token: 收到第一个token时的回调函数
            **kwargs: 传递给OpenAI API的其他参数

        返回:
            流式输出的异步生成器
        """
        # 返回一个异步生成器
        return self._create_completion_stream(messages, stream, temperature, max_tokens, on_first_token, **kwargs)

    async def _create_completion_stream(
        self,
        messages: List[Dict[str, str]],
        stream: bool,
        temperature: float,
        max_tokens: Optional[int],
        on_first_token: Optional[Callable],
        **kwargs
    ) -> AsyncGenerator[Union[ChatCompletionChunk, Any], None]:
        """创建聊天完成流"""
        # 记录请求开始时间
        logger.info(f"开始请求LLM，模型: {self.model}")

        # 创建原始响应
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            stream=stream,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )

        # 如果是流式输出，包装响应以添加日志记录
        if stream:
            async for chunk in self._wrap_stream_with_logging(response, on_first_token):
                yield chunk
        else:
            # 对于非流式输出，创建一个异步生成器来包装单个响应
            async for chunk in self._wrap_non_stream_response(response, on_first_token):
                yield chunk

    async def _wrap_stream_with_logging(
        self,
        stream: AsyncGenerator[ChatCompletionChunk, None],
        on_first_token: Optional[Callable] = None
    ) -> AsyncGenerator[ChatCompletionChunk, None]:
        """包装流式响应，添加日志记录"""
        first_token_received = False

        async for chunk in stream:
            # 检查是否是第一个有内容的token
            if not first_token_received:
                content = self.extract_content(chunk)
                if content:
                    first_token_received = True
                    first_token_time = time.time()

                    # 格式化时间戳为时:分:秒.毫秒
                    timestamp = time.strftime("%H:%M:%S", time.localtime(first_token_time))
                    ms = int((first_token_time - int(first_token_time)) * 1000)
                    formatted_time = f"{timestamp}.{ms:03d}"

                    logger.info(f"LLM返回第一个token: {formatted_time}")

                    # 调用回调函数
                    if on_first_token:
                        on_first_token(first_token_time)

            yield chunk

    async def _wrap_non_stream_response(
        self,
        response,
        on_first_token: Optional[Callable] = None
    ) -> AsyncGenerator[Any, None]:
        """包装非流式响应，转换为异步生成器格式"""
        # 记录第一个token时间
        first_token_time = time.time()

        # 格式化时间戳为时:分:秒.毫秒
        timestamp = time.strftime("%H:%M:%S", time.localtime(first_token_time))
        ms = int((first_token_time - int(first_token_time)) * 1000)
        formatted_time = f"{timestamp}.{ms:03d}"

        logger.info(f"LLM返回完整响应: {formatted_time}")

        # 调用回调函数
        if on_first_token:
            on_first_token(first_token_time)

        # 创建一个模拟的ChatCompletionChunk对象
        # 将完整响应内容作为单个chunk返回
        content = response.choices[0].message.content

        # 创建一个模拟的chunk对象，包含完整内容
        class MockChunk:
            def __init__(self, content: str):
                self.choices = [MockChoice(content)]

        class MockChoice:
            def __init__(self, content: str):
                self.delta = MockDelta(content)

        class MockDelta:
            def __init__(self, content: str):
                self.content = content

        # 返回单个chunk
        yield MockChunk(content)

    @staticmethod
    def extract_content(chunk) -> Optional[str]:
        """
        从OpenAI流式响应中提取文本内容

        参数:
            chunk: OpenAI流式响应块或模拟chunk对象

        返回:
            提取的文本内容，如果没有内容则返回None
        """
        try:
            return chunk.choices[0].delta.content
        except (AttributeError, IndexError):
            return None

# 示例使用
async def example_usage():
    client = OpenAIClient()
    messages = [
        {"role": "system", "content": "你是一个有用的AI助手。"},
        {"role": "user", "content": "请简要介绍Python语言的特点。"}
    ]

    stream = client.chat_completion(messages)

    full_response = ""
    async for chunk in stream:
        content = client.extract_content(chunk)
        if content:
            full_response += content
            print(content, end="", flush=True)

    print("\n\n完整响应:", full_response)

if __name__ == "__main__":
    asyncio.run(example_usage())