"""
自适应队列管理器

从原始StreamSpeaker中提取的队列管理逻辑，进行模块化重构。
"""

import asyncio
import logging
from typing import Any, Dict

from ..core.interfaces import QueueManager
from ..core.exceptions import QueueError, ErrorCodes

logger = logging.getLogger(__name__)


class AdaptiveQueueManager(QueueManager):
    """自适应队列管理器"""
    
    def __init__(self, initial_size: int = 10, max_size: int = 50, name: str = "queue"):
        self.initial_size = initial_size
        self.max_size = max_size
        self.name = name
        self.current_max_size = initial_size
        self._queue = asyncio.Queue(maxsize=initial_size)
        self._lock = asyncio.Lock()
        
        # 统计信息
        self._stats = {
            "full_count": 0,
            "total_enqueued": 0,
            "total_dequeued": 0,
            "expansion_count": 0,
            "max_size_reached": initial_size
        }
        
        logger.debug(f"自适应队列 '{name}' 初始化完成，初始大小: {initial_size}, 最大大小: {max_size}")
    
    async def put(self, item: Any) -> bool:
        """放入队列，如果队列满则尝试扩容
        
        Args:
            item: 要放入的项目
            
        Returns:
            是否成功放入队列
        """
        async with self._lock:
            try:
                # 尝试非阻塞放入
                self._queue.put_nowait(item)
                self._stats["total_enqueued"] += 1
                return True
            except asyncio.QueueFull:
                self._stats["full_count"] += 1
                logger.debug(f"队列 '{self.name}' 已满，当前大小: {self.current_max_size}")
                
                # 尝试扩容
                if self.current_max_size < self.max_size:
                    await self._expand_queue()
                    try:
                        self._queue.put_nowait(item)
                        self._stats["total_enqueued"] += 1
                        return True
                    except asyncio.QueueFull:
                        pass
                
                # 如果仍然满，则阻塞等待
                try:
                    await asyncio.wait_for(self._queue.put(item), timeout=5.0)
                    self._stats["total_enqueued"] += 1
                    return True
                except asyncio.TimeoutError:
                    raise QueueError(
                        f"队列 '{self.name}' 放入超时",
                        queue_name=self.name,
                        operation="put",
                        error_code=ErrorCodes.QUEUE_TIMEOUT
                    )
            except Exception as e:
                raise QueueError(
                    f"队列 '{self.name}' 放入失败: {str(e)}",
                    queue_name=self.name,
                    operation="put",
                    error_code=ErrorCodes.QUEUE_FULL
                )
    
    async def get(self) -> Any:
        """从队列获取项目
        
        Returns:
            队列中的项目
        """
        try:
            item = await self._queue.get()
            self._stats["total_dequeued"] += 1
            return item
        except Exception as e:
            raise QueueError(
                f"队列 '{self.name}' 获取失败: {str(e)}",
                queue_name=self.name,
                operation="get",
                error_code=ErrorCodes.QUEUE_EMPTY
            )
    
    def get_nowait(self) -> Any:
        """非阻塞获取项目
        
        Returns:
            队列中的项目
            
        Raises:
            QueueError: 队列为空时
        """
        try:
            item = self._queue.get_nowait()
            self._stats["total_dequeued"] += 1
            return item
        except asyncio.QueueEmpty:
            raise QueueError(
                f"队列 '{self.name}' 为空",
                queue_name=self.name,
                operation="get_nowait",
                error_code=ErrorCodes.QUEUE_EMPTY
            )
        except Exception as e:
            raise QueueError(
                f"队列 '{self.name}' 非阻塞获取失败: {str(e)}",
                queue_name=self.name,
                operation="get_nowait",
                error_code=ErrorCodes.QUEUE_EMPTY
            )
    
    def task_done(self):
        """标记任务完成"""
        try:
            self._queue.task_done()
        except Exception as e:
            logger.warning(f"队列 '{self.name}' 标记任务完成失败: {e}")
    
    async def join(self):
        """等待所有任务完成"""
        try:
            await self._queue.join()
        except Exception as e:
            logger.error(f"队列 '{self.name}' 等待任务完成失败: {e}")
    
    def qsize(self) -> int:
        """获取队列当前大小"""
        return self._queue.qsize()
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self._queue.empty()
    
    def full(self) -> bool:
        """检查队列是否满"""
        return self._queue.full()
    
    async def _expand_queue(self):
        """扩容队列"""
        new_size = min(self.current_max_size * 2, self.max_size)
        if new_size > self.current_max_size:
            try:
                # 创建新队列
                new_queue = asyncio.Queue(maxsize=new_size)
                
                # 迁移现有项目
                migrated_count = 0
                while not self._queue.empty():
                    try:
                        item = self._queue.get_nowait()
                        new_queue.put_nowait(item)
                        migrated_count += 1
                    except (asyncio.QueueEmpty, asyncio.QueueFull):
                        break
                
                self._queue = new_queue
                self.current_max_size = new_size
                self._stats["expansion_count"] += 1
                self._stats["max_size_reached"] = max(self._stats["max_size_reached"], new_size)
                
                logger.info(f"队列 '{self.name}' 已扩容至: {new_size}, 迁移项目: {migrated_count}")
            except Exception as e:
                logger.error(f"队列 '{self.name}' 扩容失败: {e}")
    
    async def clear(self):
        """清空队列"""
        async with self._lock:
            try:
                cleared_count = 0
                while not self._queue.empty():
                    try:
                        self._queue.get_nowait()
                        self._queue.task_done()
                        cleared_count += 1
                    except asyncio.QueueEmpty:
                        break
                
                logger.debug(f"队列 '{self.name}' 已清空，清理项目: {cleared_count}")
            except Exception as e:
                logger.error(f"队列 '{self.name}' 清空失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        return {
            "name": self.name,
            "initial_size": self.initial_size,
            "max_size": self.max_size,
            "current_max_size": self.current_max_size,
            "current_size": self.qsize(),
            "is_empty": self.empty(),
            "is_full": self.full(),
            "full_count": self._stats["full_count"],
            "total_enqueued": self._stats["total_enqueued"],
            "total_dequeued": self._stats["total_dequeued"],
            "expansion_count": self._stats["expansion_count"],
            "max_size_reached": self._stats["max_size_reached"],
            "utilization_rate": self.qsize() / self.current_max_size if self.current_max_size > 0 else 0.0
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self._stats = {
            "full_count": 0,
            "total_enqueued": 0,
            "total_dequeued": 0,
            "expansion_count": 0,
            "max_size_reached": self.current_max_size
        }
        logger.debug(f"队列 '{self.name}' 统计信息已重置")
