from gtts import gTTS
import os
import pygame
import tempfile
import time

def speak(text, rate=1.0, volume=1.0):
    """
    使用 Google Text-to-Speech API 将文本转换为语音
    
    参数:
        text (str): 要转换的文本
        rate (float): 语速因子 (不直接支持，但可以通过播放速度模拟)
        volume (float): 音量 (0.0 到 1.0)
    """
    # 创建临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as fp:
        temp_filename = fp.name
    
    # 使用 Google TTS 生成语音
    tts = gTTS(text=text, lang='zh-cn', slow=False)
    tts.save(temp_filename)
    
    # 使用 pygame 播放音频
    pygame.mixer.init()
    pygame.mixer.music.set_volume(volume)
    pygame.mixer.music.load(temp_filename)
    pygame.mixer.music.play()
    
    # 等待音频播放完成
    while pygame.mixer.music.get_busy():
        time.sleep(0.1)
    
    # 清理临时文件
    pygame.mixer.quit()
    os.unlink(temp_filename)

async def main():
    texts = [
        "你好，世界！",
        "这是一个使用 Google Text-to-Speech 的示例。",
        "希望这个示例对你有所帮助。",
        "祝你使用愉快！"
    ]
    
    for text in texts:
        print(f"朗读: {text}")
        speak(text, volume=0.8)
        time.sleep(1)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())