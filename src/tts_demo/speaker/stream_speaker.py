"""
流式TTS处理器 - 现代化架构

使用模块化设计的高性能TTS引擎，无历史包袱的全新实现。
"""

import asyncio
import logging
from typing import AsyncGenerator, Optional, Callable, Any, Dict

from ..core.models import TTSConfig, PlaybackState
from ..engines import TTSEngineFactory
from ..config.models import FirstChunkBreakMode

logger = logging.getLogger(__name__)


class StreamSpeaker:
    """现代化的流式TTS处理器

    基于模块化架构设计，提供高性能的实时文本到语音转换功能。
    """

    def __init__(
        self,
        voice: Optional[str] = None,
        rate: Optional[str] = None,
        volume: Optional[str] = None,
        first_chunk_size: int = 5,
        min_sentence_size: int = 20,
        max_chunk_size: int = 500,
        on_text_chunk: Optional[Callable[[str], Any]] = None,
        on_state_change: Optional[Callable[[PlaybackState], Any]] = None,
        on_stats_update: Optional[Callable[[Dict[str, Any]], Any]] = None,
        audio_frequency: int = 24000,
        queue_size: int = 10,
        max_queue_size: int = 50,
        enable_cache: bool = True,
        cache_size: int = 100,
        cache_memory_mb: float = 100.0,
        stats_update_interval: float = 5.0,
        retry_attempts: int = 3,
        retry_delay: float = 0.5,
        first_chunk_break_mode: FirstChunkBreakMode = FirstChunkBreakMode.PUNCTUATION,
        debug_mode: bool = False,
    ):
        """初始化流式TTS处理器

        Args:
            voice: 语音名称
            rate: 语速设置
            volume: 音量设置
            first_chunk_size: 首块大小
            min_sentence_size: 最小句子大小
            max_chunk_size: 最大块大小
            on_text_chunk: 文本块回调
            on_state_change: 状态变化回调
            on_stats_update: 统计更新回调
            audio_frequency: 音频采样率
            queue_size: 队列初始大小
            max_queue_size: 队列最大大小
            enable_cache: 是否启用缓存
            cache_size: 缓存大小
            cache_memory_mb: 缓存内存限制
            stats_update_interval: 统计更新间隔
            retry_attempts: 重试次数
            retry_delay: 重试延迟
            first_chunk_break_mode: 首块断句模式
            debug_mode: 调试模式
        """
        # 创建配置
        self.config = TTSConfig(
            voice=voice or "zh-CN-YunjianNeural",
            rate=rate or "+0%",
            volume=volume or "+0%",
            first_chunk_size=first_chunk_size,
            min_sentence_size=min_sentence_size,
            max_chunk_size=max_chunk_size,
            queue_size=queue_size,
            max_queue_size=max_queue_size,
            enable_cache=enable_cache,
            cache_size=cache_size,
            cache_memory_mb=cache_memory_mb,
            debug_mode=debug_mode,
            audio_frequency=audio_frequency,
            stats_update_interval=stats_update_interval,
            retry_attempts=retry_attempts,
            retry_delay=retry_delay,
            first_chunk_break_mode=first_chunk_break_mode
        )

        # 准备事件处理器
        event_handlers = {}

        if on_text_chunk:
            def text_chunk_handler(event):
                on_text_chunk(event.chunk.content)
            event_handlers['text_chunk'] = text_chunk_handler

        if on_state_change:
            def state_change_handler(event):
                on_state_change(event.new_state)
            event_handlers['playback_state'] = state_change_handler

        if on_stats_update:
            def stats_update_handler(event):
                on_stats_update(event.stats.to_dict())
            event_handlers['stats_update'] = stats_update_handler

        # 创建TTS引擎
        self._engine = TTSEngineFactory.create_stream_engine(
            self.config,
            event_handlers=event_handlers
        )

        logger.info(f"StreamSpeaker初始化完成: voice={self.config.voice}, debug={self.config.debug_mode}")

    async def start(self):
        """启动TTS引擎"""
        if self._engine:
            await self._engine.start()

    async def start_stream_processing(self, stream: AsyncGenerator[Any, None],
                                    content_extractor: Callable[[Any], Optional[str]]):
        """开始流式处理

        Args:
            stream: 异步生成器流
            content_extractor: 内容提取函数
        """
        try:
            await self._engine.start()
            await self._engine.process_stream(stream, content_extractor)
        finally:
            await self._engine.stop()

    async def stop(self):
        """停止处理"""
        if self._engine:
            await self._engine.stop()

    async def force_stop(self):
        """强制停止处理（立即停止音频播放）"""
        if self._engine:
            # 立即停止音频播放
            if hasattr(self._engine, 'audio_player'):
                self._engine.audio_player.stop()
            # 停止引擎
            await self._engine.stop()

    async def process_stream(self, stream: AsyncGenerator[Any, None],
                           content_extractor: Callable[[Any], Optional[str]]):
        """处理流式数据（兼容方法）

        Args:
            stream: 异步生成器流
            content_extractor: 内容提取函数
        """
        if self._engine:
            await self._engine.process_stream(stream, content_extractor)

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self._engine:
            return {}

        stats = self._engine.get_stats()
        return stats.to_dict()

    def get_detailed_stats(self) -> Dict[str, Any]:
        """获取详细统计信息"""
        if not self._engine:
            return {}

        return self._engine.get_component_stats()

    @property
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._engine.is_running if self._engine else False

    @property
    def state(self) -> PlaybackState:
        """获取当前状态"""
        return self._engine.get_state() if self._engine else PlaybackState.STOPPED

    @property
    def playback_state(self) -> PlaybackState:
        """获取播放状态（兼容属性）"""
        return self.state

    @property
    def first_chunk_size(self) -> int:
        """首块大小"""
        return self.config.first_chunk_size

    @first_chunk_size.setter
    def first_chunk_size(self, value: int):
        """设置首块大小"""
        self.config.first_chunk_size = value

    @property
    def min_sentence_size(self) -> int:
        """最小句子大小"""
        return self.config.min_sentence_size

    @min_sentence_size.setter
    def min_sentence_size(self, value: int):
        """设置最小句子大小"""
        self.config.min_sentence_size = value

    @property
    def max_chunk_size(self) -> int:
        """最大块大小"""
        return self.config.max_chunk_size

    @max_chunk_size.setter
    def max_chunk_size(self, value: int):
        """设置最大块大小"""
        self.config.max_chunk_size = value

    @property
    def first_chunk_break_mode(self) -> FirstChunkBreakMode:
        """首块断句模式"""
        return self.config.first_chunk_break_mode

    @first_chunk_break_mode.setter
    def first_chunk_break_mode(self, value: FirstChunkBreakMode):
        """设置首块断句模式"""
        self.config.first_chunk_break_mode = value

    def add_event_handler(self, event_type: str, handler: Callable):
        """添加事件处理器"""
        if self._engine:
            self._engine.add_event_handler(event_type, handler)

    def remove_event_handler(self, event_type: str, handler: Callable):
        """移除事件处理器"""
        if self._engine:
            self._engine.remove_event_handler(event_type, handler)

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._engine.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._engine.stop()

    def __del__(self):
        """析构函数"""
        try:
            if self._engine and self._engine.is_running:
                # 在事件循环中安全地停止引擎
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(self._engine.stop())
        except Exception as e:
            logger.debug(f"清理StreamSpeaker时出错: {e}")


# 为了保持API简洁，提供一个便捷的创建函数
def create_stream_speaker(**kwargs) -> StreamSpeaker:
    """创建流式TTS处理器的便捷函数

    Args:
        **kwargs: StreamSpeaker的初始化参数

    Returns:
        StreamSpeaker实例
    """
    return StreamSpeaker(**kwargs)


# 提供一个异步上下文管理器的便捷类
class StreamSpeakerContext:
    """流式TTS处理器的异步上下文管理器"""

    def __init__(self, **kwargs):
        self.kwargs = kwargs
        self.speaker = None

    async def __aenter__(self):
        self.speaker = StreamSpeaker(**self.kwargs)
        await self.speaker._engine.start()
        return self.speaker

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.speaker:
            await self.speaker._engine.stop()


def stream_speaker_context(**kwargs):
    """创建流式TTS处理器的异步上下文管理器

    Usage:
        async with stream_speaker_context(voice="zh-CN-YunjianNeural") as speaker:
            await speaker.start_stream_processing(stream, extractor)
    """
    return StreamSpeakerContext(**kwargs)
