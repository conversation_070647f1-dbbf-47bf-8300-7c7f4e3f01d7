import asyncio
import edge_tts
import pygame
import tempfile
import os
import time

async def speak_async(text, voice="zh-CN-XiaoxiaoNeural", rate="+0%", volume="+0%"):
    """
    使用 Microsoft Edge TTS 将文本转换为语音
    
    参数:
        text (str): 要转换的文本
        voice (str): 语音名称
        rate (str): 语速 (如 "+0%", "+50%", "-50%")
        volume (str): 音量 (如 "+0%", "+50%", "-50%")
    """
    # 创建临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as fp:
        temp_filename = fp.name
    
    # 使用 Edge TTS 生成语音
    communicate = edge_tts.Communicate(text, voice, rate=rate, volume=volume)
    await communicate.save(temp_filename)
    
    # 使用 pygame 播放音频
    pygame.mixer.init()
    pygame.mixer.music.load(temp_filename)
    pygame.mixer.music.play()
    
    # 等待音频播放完成
    while pygame.mixer.music.get_busy():
        time.sleep(0.1)
    
    # 清理临时文件
    pygame.mixer.quit()
    os.unlink(temp_filename)

def speak(text, voice="zh-CN-XiaoxiaoNeural", rate="+0%", volume="+0%"):
    """同步版本的speak函数"""
    asyncio.run(speak_async(text, voice, rate, volume))

async def list_voices():
    """列出所有可用的语音"""
    voices = await edge_tts.list_voices()
    print("可用的中文语音:")
    for voice in voices:
        if "zh-CN" in voice["ShortName"]:
            print(f" - {voice['ShortName']}: {voice['FriendlyName']}")

async def main():
    # 列出可用语音
    await list_voices()
    
    texts = [
        "你好，世界！",
        "这是一个使用 Microsoft Edge TTS 的示例。",
        "这个语音合成引擎提供更加逼真的语音效果。",
        "希望这个示例对你有所帮助。",
        "祝你使用愉快！"
    ]
    
    for text in texts:
        print(f"朗读: {text}")
        await speak_async(text)
        await asyncio.sleep(1)

if __name__ == "__main__":
    asyncio.run(main())