import pyttsx3
import asyncio

def speak(text, rate=150, volume=1.0):
    engine = pyttsx3.init()
    
    # 设置语音属性
    engine.setProperty('rate', rate)  # 语速
    engine.setProperty('volume', volume)  # 音量
    
    # 选择语音
    voices = engine.getProperty('voices')
    found_chinese = False
    for voice in voices:
        try:
            if voice.languages and 'zh' in voice.languages[0].decode().lower():
                engine.setProperty('voice', voice.id)
                found_chinese = True
                break
        except (AttributeError, IndexError, UnicodeDecodeError):
            continue
    
    if not found_chinese and voices:
        # 如果没有找到中文语音，使用默认语音
        print("未找到中文语音，使用默认语音")
    
    # 朗读文本
    engine.say(text)
    engine.runAndWait()

async def main():
    texts = [
        "你好，世界！",
        "这是一个使用 pyttsx3 库的示例。",
        "调整语速和音量可以改变朗读的效果。",
        "希望这个示例对你有所帮助。",
        "祝你使用愉快！"
    ]
    
    for text in texts:
        print(f"朗读: {text}")
        speak(text, rate=160, volume=0.8)  # 调整语速和音量
        await asyncio.sleep(1)

if __name__ == "__main__":
    asyncio.run(main())