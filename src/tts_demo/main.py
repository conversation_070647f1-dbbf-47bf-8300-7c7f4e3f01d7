"""
大模型流式TTS演示程序主模块

该模块提供了将大模型的流式文本输出实时转换为语音的核心功能。
"""

import asyncio
import argparse
import json
import logging
import signal
from typing import Optional, Dict, Any

from .config import get_app_config
from .llm import OpenAIClient, LangflowClient
from .speaker import StreamSpeaker, PlaybackState

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("TTS-Demo")

# 全局变量，用于保存speaker实例
global_speaker = None


def handle_signal(signum, frame):
    """处理信号（如Ctrl+C）"""
    _ = signum, frame  # 忽略未使用的参数
    print("\n接收到中断信号，正在停止...")
    if global_speaker:
        # 创建一个新的事件循环来停止speaker
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(global_speaker.stop())
        loop.close()
    exit(0)


# 注册信号处理函数
signal.signal(signal.SIGINT, handle_signal)
signal.signal(signal.SIGTERM, handle_signal)


def on_state_change(state: PlaybackState):
    """状态变化回调函数"""
    state_names = {
        PlaybackState.STOPPED: "已停止",
        PlaybackState.PLAYING: "播放中",
        PlaybackState.PAUSED: "已暂停"
    }
    logger.info(f"播放状态变化: {state_names.get(state, '未知')}")


def on_stats_update(stats: Dict[str, Any]):
    """统计信息更新回调函数"""
    logger.debug(f"统计信息更新: 文本块={stats['total_chunks']}, 字符数={stats['total_characters']}, "
                f"音频时长={stats['total_audio_time']:.2f}秒, 缓存命中率={stats['cache_hit_rate']*100:.1f}%")


async def run_llm_tts_demo(
    prompt: Optional[str] = None,
    config_path: Optional[str] = None,
    **kwargs
):
    """
    运行大模型流式TTS演示

    参数:
        prompt: 发送给大模型的提示词
        config_path: 配置文件路径
        **kwargs: 其他配置参数，会覆盖配置文件中的设置
    """
    global global_speaker

    # 加载配置
    config = get_app_config(config_path)

    # 使用kwargs覆盖配置
    for key, value in kwargs.items():
        if value is not None:
            # 将参数映射到配置结构
            if key in ['model', 'api_key', 'base_url']:
                setattr(config.llm, key, value)
            elif key in ['voice', 'rate', 'volume']:
                setattr(config.tts, key, value)
            elif key in ['first_chunk_size', 'min_sentence_size', 'max_chunk_size', 'custom_words']:
                setattr(config.chunking, key, value)
            elif key in ['enable_cache', 'cache_size']:
                setattr(config.cache, key, value)
            elif key in ['debug', 'save_stats', 'stats_update_interval', 'retry_attempts']:
                setattr(config.advanced, key, value)
            elif key.startswith('langflow_'):
                attr_name = key.replace('langflow_', '')
                if hasattr(config.langflow, attr_name):
                    setattr(config.langflow, attr_name, value)
            elif key == 'use_langflow':
                config.use_langflow = value

    # 设置日志级别
    if config.advanced.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # 使用默认提示词
    prompt = prompt or "你是？"

    # 根据配置选择LLM客户端
    if config.use_langflow:
        logger.info(f"正在使用Langflow工作流处理提示: {prompt}")
        logger.info(f"Langflow设置: 服务器={config.langflow.base_url}, 工作流ID={config.langflow.flow_id}")

        client = LangflowClient(
            base_url=config.langflow.base_url,
            api_key=config.langflow.api_key,
            flow_id=config.langflow.flow_id
        )
    else:
        logger.info(f"正在使用OpenAI模型 {config.llm.model} 处理提示: {prompt}")

        client = OpenAIClient(
            api_key=config.llm.api_key,
            base_url=config.llm.base_url,
            model=config.llm.model
        )

    logger.info(f"语音设置: 声音={config.tts.voice}, 语速={config.tts.rate}, 音量={config.tts.volume}")
    print("正在生成回复，请稍候...\n")

    # 准备消息
    messages = [
        {"role": "system", "content": "你是一个有用的AI助手，请用中文回答问题。内容要适合语音讲解,尽量少使用英文缩写"},
        {"role": "user", "content": prompt}
    ]

    # 初始化现代化流式TTS处理器
    speaker = StreamSpeaker(
        voice=config.tts.voice,
        rate=config.tts.rate,
        volume=config.tts.volume,
        first_chunk_size=config.chunking.first_chunk_size,
        min_sentence_size=config.chunking.min_sentence_size,
        max_chunk_size=config.chunking.max_chunk_size,
        on_text_chunk=lambda text: print(f"朗读: {text}"),
        on_state_change=on_state_change,
        on_stats_update=on_stats_update if config.advanced.debug else None,
        queue_size=config.queue.queue_size,
        max_queue_size=config.queue.max_queue_size,
        enable_cache=config.cache.enable_cache,
        cache_size=config.cache.cache_size,
        cache_memory_mb=config.cache.cache_memory_mb,
        stats_update_interval=config.advanced.stats_update_interval,
        retry_attempts=config.advanced.retry_attempts,
        first_chunk_break_mode=config.chunking.first_chunk_break_mode,
        debug_mode=config.advanced.debug
    )

    # 处理自定义词语
    if config.chunking.custom_words:
        words = [word.strip() for word in config.chunking.custom_words.split(",") if word.strip()]
        for word in words:
            speaker.add_custom_word(word)
            logger.info(f"已添加自定义词语: {word}")

    # 保存到全局变量
    global_speaker = speaker

    # 启动TTS处理
    await speaker.start()

    try:
        # 获取流式响应
        if config.use_langflow:
            stream = await client.chat_completion(
                messages,
                session_id=config.langflow.session_id,
                tweaks=config.langflow.tweaks
            )
        else:
            stream = await client.chat_completion(messages)

        # 处理流式输出
        await speaker.process_stream(stream, client.extract_content)

        print("\n回复已完成并朗读完毕。")

        # 输出关键时间指标
        stats = speaker.get_stats()
        logger.info(f"从LLM返回第一个token到TTS开始朗读的延迟: {stats['time_to_first_audio']:.3f}秒")

        # 保存统计信息
        if config.advanced.save_stats:
            with open("tts_stats.json", "w", encoding="utf-8") as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            print(f"统计信息已保存到 tts_stats.json")

    except Exception as e:
        logger.error(f"发生错误: {e}")
        print(f"发生错误: {e}")
    finally:
        # 停止TTS处理
        await speaker.stop()
        global_speaker = None

        return {"speaker": speaker}


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(description="大模型流式TTS演示")

    # 基本参数
    parser.add_argument("--prompt", help="发送给大模型的提示词")
    parser.add_argument("--config", help="配置文件路径")

    # LLM参数
    llm_group = parser.add_argument_group("LLM参数")
    llm_group.add_argument("--model", help="使用的模型名称")
    llm_group.add_argument("--api-key", help="OpenAI API密钥")
    llm_group.add_argument("--base-url", help="OpenAI API基础URL")

    # Langflow参数
    langflow_group = parser.add_argument_group("Langflow参数")
    langflow_group.add_argument("--use-langflow", action="store_true", help="使用Langflow工作流")
    langflow_group.add_argument("--langflow-base-url", help="Langflow服务器URL")
    langflow_group.add_argument("--langflow-api-key", help="Langflow API密钥")
    langflow_group.add_argument("--langflow-flow-id", help="Langflow工作流ID")
    langflow_group.add_argument("--langflow-session-id", help="Langflow会话ID")
    langflow_group.add_argument("--langflow-tweaks", help="Langflow组件调整参数 (JSON格式)")

    # TTS参数
    tts_group = parser.add_argument_group("TTS参数")
    tts_group.add_argument("--voice", help="TTS语音名称")
    tts_group.add_argument("--rate", help="语速")
    tts_group.add_argument("--volume", help="音量")

    # 分块参数
    chunk_group = parser.add_argument_group("分块参数")
    chunk_group.add_argument("--first-chunk-size", type=int, help="第一个文本块的最大词数")
    chunk_group.add_argument("--min-sentence-size", type=int, help="最小句子大小")
    chunk_group.add_argument("--max-chunk-size", type=int, help="最大处理块大小")
    chunk_group.add_argument("--custom-words", help="自定义词语，逗号分隔")



    # 缓存参数
    cache_group = parser.add_argument_group("缓存参数")
    cache_group.add_argument("--no-cache", action="store_true", help="禁用缓存")
    cache_group.add_argument("--cache-size", type=int, help="缓存大小")

    # 高级参数
    advanced_group = parser.add_argument_group("高级参数")
    advanced_group.add_argument("--debug", action="store_true", help="启用调试模式")
    advanced_group.add_argument("--save-stats", action="store_true", help="保存统计信息")
    advanced_group.add_argument("--stats-update-interval", type=float, help="统计信息更新间隔")
    advanced_group.add_argument("--retry-attempts", type=int, help="重试次数")

    return parser


def main():
    """命令行入口函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    # 准备参数字典
    kwargs = {}

    # 处理命令行参数
    for arg_name, arg_value in vars(args).items():
        if arg_value is not None:
            # 转换参数名格式
            param_name = arg_name.replace('-', '_')

            # 特殊处理
            if param_name == 'no_cache':
                kwargs['enable_cache'] = not arg_value
            elif param_name == 'langflow_tweaks':
                try:
                    kwargs[param_name] = json.loads(arg_value)
                except json.JSONDecodeError as e:
                    logger.error(f"Langflow tweaks参数格式错误: {e}")
                    return 1
            else:
                kwargs[param_name] = arg_value

    try:
        # 运行主程序
        asyncio.run(run_llm_tts_demo(
            prompt=args.prompt,
            config_path=args.config,
            **kwargs
        ))
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行错误: {e}")
        return 1

    return 0


if __name__ == "__main__":
    main()
