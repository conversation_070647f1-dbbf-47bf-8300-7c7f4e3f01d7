"""
音频缓存管理器

从原始StreamSpeaker中提取的缓存管理逻辑，进行模块化重构。
"""

import io
import threading
import logging
from collections import OrderedDict
from typing import Optional

from ..core.interfaces import CacheManager
from ..core.models import AudioData
from ..core.exceptions import CacheError, ErrorCodes

logger = logging.getLogger(__name__)


class SmartAudioCache(CacheManager):
    """智能音频缓存管理器"""
    
    def __init__(self, max_items: int = 100, max_memory_mb: float = 100.0):
        self.max_items = max_items
        self.max_memory_mb = max_memory_mb
        self._cache = OrderedDict()
        self._cache_size_bytes = 0
        self._lock = threading.Lock()
        self._stats = {
            "hits": 0,
            "misses": 0,
            "puts": 0,
            "evictions": 0,
            "errors": 0
        }
        
        logger.debug(f"音频缓存初始化完成，最大项数: {max_items}, 最大内存: {max_memory_mb}MB")
    
    def get_cache_size_bytes(self) -> int:
        """获取缓存占用的字节数"""
        with self._lock:
            return self._cache_size_bytes
    
    def get_cache_count(self) -> int:
        """获取缓存项数量"""
        with self._lock:
            return len(self._cache)
    
    def _estimate_audio_size(self, audio_data: AudioData) -> int:
        """估算音频数据大小"""
        try:
            return audio_data.get_size_bytes()
        except Exception as e:
            logger.warning(f"估算音频大小失败: {e}")
            return 0
    
    def _cleanup_cache(self):
        """清理缓存以满足内存限制"""
        current_memory_mb = self._cache_size_bytes / (1024 * 1024)
        
        # 如果超过内存限制，移除最旧的项
        while (current_memory_mb > self.max_memory_mb or
               len(self._cache) > self.max_items) and self._cache:
            
            key, audio_data = self._cache.popitem(last=False)
            removed_size = self._estimate_audio_size(audio_data)
            self._cache_size_bytes -= removed_size
            current_memory_mb = self._cache_size_bytes / (1024 * 1024)
            self._stats["evictions"] += 1
            
            logger.debug(f"清理缓存项: {key[:8]}..., 释放内存: {removed_size/1024:.1f}KB")
    
    def get(self, key: str) -> Optional[AudioData]:
        """从缓存获取音频数据
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的音频数据，如果没有找到则返回None
        """
        with self._lock:
            try:
                if key in self._cache:
                    cached_data = self._cache[key]
                    
                    # 创建音频数据副本
                    audio_data = cached_data.copy()
                    
                    # 更新缓存顺序（最近使用）
                    self._cache.move_to_end(key)
                    
                    self._stats["hits"] += 1
                    logger.debug(f"缓存命中: {key[:8]}...")
                    return audio_data
                else:
                    self._stats["misses"] += 1
                    logger.debug(f"缓存未命中: {key[:8]}...")
                    return None
            except Exception as e:
                self._stats["errors"] += 1
                raise CacheError(
                    f"缓存获取失败: {str(e)}",
                    cache_key=key,
                    operation="get",
                    error_code=ErrorCodes.CACHE_GET_FAILED
                )
    
    def put(self, key: str, audio_data: AudioData) -> bool:
        """添加音频数据到缓存
        
        Args:
            key: 缓存键
            audio_data: 音频数据
            
        Returns:
            是否成功添加到缓存
        """
        with self._lock:
            try:
                # 估算音频大小
                audio_size = self._estimate_audio_size(audio_data)
                
                # 检查单个音频是否超过内存限制
                if audio_size / (1024 * 1024) > self.max_memory_mb:
                    logger.warning(f"音频数据过大({audio_size/1024/1024:.1f}MB)，跳过缓存")
                    return False
                
                # 如果键已存在，先移除旧的
                if key in self._cache:
                    old_audio = self._cache[key]
                    old_size = self._estimate_audio_size(old_audio)
                    self._cache_size_bytes -= old_size
                    del self._cache[key]
                
                # 创建音频数据副本并添加到缓存
                cached_data = audio_data.copy()
                self._cache[key] = cached_data
                self._cache_size_bytes += audio_size
                
                # 清理缓存
                self._cleanup_cache()
                
                self._stats["puts"] += 1
                logger.debug(f"缓存添加成功: {key[:8]}..., 大小: {audio_size/1024:.1f}KB")
                return True
                
            except Exception as e:
                self._stats["errors"] += 1
                raise CacheError(
                    f"缓存添加失败: {str(e)}",
                    cache_key=key,
                    operation="put",
                    error_code=ErrorCodes.CACHE_PUT_FAILED,
                    context={"audio_size": audio_size if 'audio_size' in locals() else 0}
                )
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            try:
                self._cache.clear()
                self._cache_size_bytes = 0
                logger.debug("缓存已清空")
            except Exception as e:
                self._stats["errors"] += 1
                logger.error(f"清空缓存失败: {e}")
    
    def remove(self, key: str) -> bool:
        """移除指定缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功移除
        """
        with self._lock:
            try:
                if key in self._cache:
                    audio_data = self._cache[key]
                    removed_size = self._estimate_audio_size(audio_data)
                    del self._cache[key]
                    self._cache_size_bytes -= removed_size
                    logger.debug(f"缓存项已移除: {key[:8]}...")
                    return True
                return False
            except Exception as e:
                self._stats["errors"] += 1
                logger.error(f"移除缓存项失败: {e}")
                return False
    
    def get_stats(self) -> dict:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats["hits"] + self._stats["misses"]
            hit_rate = self._stats["hits"] / total_requests if total_requests > 0 else 0.0
            
            return {
                "max_items": self.max_items,
                "max_memory_mb": self.max_memory_mb,
                "current_items": len(self._cache),
                "current_memory_mb": self._cache_size_bytes / (1024 * 1024),
                "memory_usage_percent": (self._cache_size_bytes / (1024 * 1024)) / self.max_memory_mb * 100,
                "hits": self._stats["hits"],
                "misses": self._stats["misses"],
                "puts": self._stats["puts"],
                "evictions": self._stats["evictions"],
                "errors": self._stats["errors"],
                "hit_rate": hit_rate,
                "total_requests": total_requests
            }
    
    def is_full(self) -> bool:
        """检查缓存是否已满"""
        with self._lock:
            memory_full = (self._cache_size_bytes / (1024 * 1024)) >= self.max_memory_mb
            items_full = len(self._cache) >= self.max_items
            return memory_full or items_full
    
    def get_memory_usage_mb(self) -> float:
        """获取内存使用量（MB）"""
        with self._lock:
            return self._cache_size_bytes / (1024 * 1024)
