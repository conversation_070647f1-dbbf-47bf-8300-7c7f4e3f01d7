"""
音频生成器实现

提供各种TTS引擎的音频生成实现。
这个文件现在主要用于向后兼容，新的引擎实现在单独的文件中。
"""

import logging

# 导入各种TTS引擎
from .edge_tts_generator import EdgeTTSGenerator
from .f5_tts_generator import F5TTSGenerator
from .megatts3_generator import MegaTTS3Generator
from .cosyvoice2_generator import CosyVoice2Generator
from .openai_tts_generator import OpenAITTSGenerator

# 导入工厂和管理器
from .engine_factory import TTSEngineFactory, TTSEngineManager, get_engine_factory, create_engine_manager

logger = logging.getLogger(__name__)

# 向后兼容性导出
__all__ = [
    'EdgeTTSGenerator',
    'F5TTSGenerator',
    'MegaTTS3Generator',
    'CosyVoice2Generator',
    'OpenAITTSGenerator',
    'TTSEngineFactory',
    'TTSEngineManager',
    'get_engine_factory',
    'create_engine_manager'
]

logger.debug("音频生成器模块已加载，支持多种TTS引擎")
