"""
OpenAI TTS音频生成器实现

使用OpenAI的TTS API进行语音合成。
"""

import io
import time
import hashlib
import logging
import asyncio
from typing import Optional, Dict, Any, List
import aiohttp

from ..core.interfaces import AudioGenerator
from ..core.models import AudioData
from ..core.exceptions import AudioGenerationError, ErrorCodes

logger = logging.getLogger(__name__)


class OpenAITTSGenerator(AudioGenerator):
    """OpenAI TTS音频生成器"""
    
    def __init__(self, 
                 api_key: str,
                 model: str = "tts-1",
                 base_url: str = "https://api.openai.com/v1",
                 timeout: float = 30.0,
                 retry_attempts: int = 3, 
                 retry_delay: float = 0.5,
                 **kwargs):
        """
        初始化OpenAI TTS生成器
        
        Args:
            api_key: OpenAI API密钥
            model: TTS模型 ("tts-1", "tts-1-hd")
            base_url: API基础URL
            timeout: 请求超时时间
            retry_attempts: 重试次数
            retry_delay: 重试延迟
            **kwargs: 其他配置参数
        """
        self.api_key = api_key
        self.model = model
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        self.config = kwargs
        
        # HTTP会话
        self._session = None
        self._initialized = False
        
        # OpenAI TTS支持的语音
        self.supported_voices = [
            "alloy", "echo", "fable", "onyx", "nova", "shimmer"
        ]
        
        logger.debug(f"OpenAI TTS生成器初始化完成，模型: {model}")
    
    async def _initialize(self):
        """初始化HTTP会话"""
        if self._initialized:
            return
        
        try:
            # 创建HTTP会话
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(timeout=timeout)
            
            self._initialized = True
            logger.info("OpenAI TTS生成器初始化完成")
            
        except Exception as e:
            logger.error(f"OpenAI TTS初始化失败: {e}")
            raise AudioGenerationError(
                f"OpenAI TTS初始化失败: {str(e)}",
                error_code=ErrorCodes.AUDIO_ENGINE_UNAVAILABLE,
                context={"model": self.model, "base_url": self.base_url}
            )
    
    async def generate_audio(self, text: str, voice: str, rate: str, volume: str) -> Optional[AudioData]:
        """
        生成音频数据
        
        Args:
            text: 要转换的文本
            voice: 语音名称
            rate: 语速（OpenAI TTS不支持动态调整，忽略此参数）
            volume: 音量（OpenAI TTS不支持动态调整，忽略此参数）
            
        Returns:
            生成的音频数据，失败时返回None
        """
        if not text.strip():
            logger.warning("文本为空，跳过音频生成")
            return None
        
        # 确保已初始化
        await self._initialize()
        
        # 映射语音名称
        openai_voice = self._map_voice(voice)
        
        # 记录开始时间
        start_time = time.time()
        
        # 重试机制
        for attempt in range(self.retry_attempts):
            try:
                logger.debug(f"OpenAI TTS生成音频: '{text[:50]}{'...' if len(text) > 50 else ''}'")
                
                # 调用OpenAI TTS API
                audio_data = await self._call_openai_api(text, openai_voice)
                
                if audio_data is None:
                    logger.warning(f"OpenAI TTS未生成音频数据: '{text[:20]}{'...' if len(text) > 20 else ''}'")
                    if attempt < self.retry_attempts - 1:
                        logger.info(f"重试 ({attempt + 1}/{self.retry_attempts})...")
                        await asyncio.sleep(self.retry_delay)
                        continue
                    return None
                
                # 创建音频数据对象
                result = AudioData(
                    data=audio_data,
                    format="mp3",  # OpenAI TTS输出MP3格式
                    sample_rate=24000,  # OpenAI TTS的采样率
                    text=text,
                    timestamp=time.time(),
                    metadata={
                        "voice": voice,
                        "openai_voice": openai_voice,
                        "rate": rate,
                        "volume": volume,
                        "generation_time": time.time() - start_time,
                        "attempt": attempt + 1,
                        "engine": "openai_tts",
                        "model": self.model
                    }
                )
                
                logger.debug(f"OpenAI TTS音频生成成功: '{text[:20]}{'...' if len(text) > 20 else ''}', 耗时: {time.time() - start_time:.3f}秒")
                return result
                
            except Exception as e:
                logger.error(f"OpenAI TTS音频生成错误 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    logger.info(f"重试 ({attempt + 1}/{self.retry_attempts})...")
                    await asyncio.sleep(self.retry_delay)
                else:
                    raise AudioGenerationError(
                        f"OpenAI TTS音频生成失败，已重试 {self.retry_attempts} 次: {str(e)}",
                        text=text,
                        voice=voice,
                        error_code=ErrorCodes.AUDIO_GENERATION_FAILED,
                        context={
                            "voice": voice,
                            "openai_voice": openai_voice,
                            "rate": rate,
                            "volume": volume,
                            "attempts": self.retry_attempts,
                            "engine": "openai_tts",
                            "model": self.model
                        }
                    )
        
        return None
    
    async def _call_openai_api(self, text: str, voice: str) -> Optional[io.BytesIO]:
        """
        调用OpenAI TTS API生成音频
        
        Args:
            text: 文本内容
            voice: OpenAI语音名称
            
        Returns:
            音频数据流
        """
        try:
            # 准备请求数据
            request_data = {
                "model": self.model,
                "input": text,
                "voice": voice,
                "response_format": "mp3"
            }
            
            # 准备请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 发送请求
            url = f"{self.base_url}/audio/speech"
            async with self._session.post(
                url,
                json=request_data,
                headers=headers
            ) as response:
                
                if response.status == 200:
                    # 读取音频数据
                    audio_bytes = await response.read()
                    audio_buffer = io.BytesIO(audio_bytes)
                    return audio_buffer
                else:
                    error_text = await response.text()
                    logger.error(f"OpenAI TTS API错误 {response.status}: {error_text}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error("OpenAI TTS API请求超时")
            return None
        except Exception as e:
            logger.error(f"OpenAI TTS API调用出错: {e}")
            raise
    
    def _map_voice(self, voice: str) -> str:
        """
        将通用语音名称映射到OpenAI语音名称
        
        Args:
            voice: 通用语音名称
            
        Returns:
            OpenAI语音名称
        """
        # 语音映射表
        voice_mapping = {
            # 中文语音映射
            "zh-CN-YunjianNeural": "nova",
            "zh-CN-XiaoxiaoNeural": "shimmer",
            "zh-CN-YunxiNeural": "onyx",
            "zh-CN-YunyangNeural": "echo",
            
            # 英文语音映射
            "en-US-AriaNeural": "nova",
            "en-US-JennyNeural": "shimmer",
            "en-US-GuyNeural": "onyx",
            "en-US-DavisNeural": "echo",
            
            # 直接映射OpenAI语音
            "alloy": "alloy",
            "echo": "echo",
            "fable": "fable",
            "onyx": "onyx",
            "nova": "nova",
            "shimmer": "shimmer"
        }
        
        # 返回映射的语音，如果没有找到则使用默认语音
        return voice_mapping.get(voice, "nova")
    
    def get_supported_voices(self) -> List[str]:
        """获取支持的语音列表"""
        return self.supported_voices.copy()
    
    async def warmup(self, text: str, voice: str, rate: str, volume: str):
        """
        预热音频引擎，减少首次延迟
        
        Args:
            text: 预热文本
            voice: 语音名称
            rate: 语速
            volume: 音量
        """
        try:
            logger.debug(f"开始预热 OpenAI TTS 引擎，文本: '{text}'")
            start_time = time.time()
            
            # 确保已初始化
            await self._initialize()
            
            # 生成一小段音频进行预热
            openai_voice = self._map_voice(voice)
            await self._call_openai_api(text, openai_voice)
            
            elapsed = time.time() - start_time
            logger.debug(f"OpenAI TTS 引擎预热完成，耗时: {elapsed:.3f}秒")
            
        except Exception as e:
            logger.debug(f"OpenAI TTS预热过程中出现错误: {e}")
            # 预热失败不抛出异常，只记录日志
    
    def generate_cache_key(self, text: str, voice: str, rate: str, volume: str) -> str:
        """
        生成缓存键
        
        Args:
            text: 文本内容
            voice: 语音名称
            rate: 语速
            volume: 音量
            
        Returns:
            缓存键
        """
        # OpenAI TTS不支持动态调整语速和音量，所以缓存键中不包含这些参数
        openai_voice = self._map_voice(voice)
        key_data = f"openai_tts|{text}|{openai_voice}|{self.model}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取生成器统计信息"""
        return {
            "generator_type": "OpenAI TTS",
            "model": self.model,
            "base_url": self.base_url,
            "timeout": self.timeout,
            "initialized": self._initialized,
            "retry_attempts": self.retry_attempts,
            "retry_delay": self.retry_delay,
            "supported_voices": self.supported_voices,
            "config": self.config
        }
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self._session is not None:
                await self._session.close()
                self._session = None
            
            self._initialized = False
            logger.debug("OpenAI TTS生成器资源已清理")
            
        except Exception as e:
            logger.error(f"OpenAI TTS清理资源时出错: {e}")
