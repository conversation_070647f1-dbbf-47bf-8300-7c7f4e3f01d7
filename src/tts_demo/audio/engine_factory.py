"""
TTS引擎工厂和注册系统

提供统一的TTS引擎创建和管理接口，支持多种TTS引擎的动态切换。
"""

import logging
from typing import Dict, Type, Optional, Any, List, Callable
from abc import ABC, abstractmethod

from ..core.interfaces import AudioGenerator
from ..core.models import TTSEngineType, TTSConfig, TTSEngineConfig
from ..core.exceptions import AudioGenerationError, ErrorCodes

# 导入各种TTS引擎
from .edge_tts_generator import EdgeTTSGenerator
from .f5_tts_generator import F5TTSGenerator
from .megatts3_generator import MegaTTS3Generator
from .cosyvoice2_generator import CosyVoice2Generator
from .openai_tts_generator import OpenAITTSGenerator

logger = logging.getLogger(__name__)


class TTSEngineFactory:
    """TTS引擎工厂类"""
    
    def __init__(self):
        """初始化工厂"""
        self._engines: Dict[TTSEngineType, Type[AudioGenerator]] = {}
        self._engine_configs: Dict[TTSEngineType, Dict[str, Any]] = {}
        self._engine_validators: Dict[TTSEngineType, Callable] = {}
        
        # 注册默认引擎
        self._register_default_engines()
    
    def _register_default_engines(self):
        """注册默认的TTS引擎"""
        # Edge TTS
        self.register_engine(
            TTSEngineType.EDGE_TTS,
            EdgeTTSGenerator,
            validator=self._validate_edge_tts_config
        )
        
        # F5-TTS
        self.register_engine(
            TTSEngineType.F5_TTS,
            F5TTSGenerator,
            validator=self._validate_f5_tts_config
        )
        
        # MegaTTS3
        self.register_engine(
            TTSEngineType.MEGATTS3,
            MegaTTS3Generator,
            validator=self._validate_megatts3_config
        )
        
        # CosyVoice2
        self.register_engine(
            TTSEngineType.COSYVOICE2,
            CosyVoice2Generator,
            validator=self._validate_cosyvoice2_config
        )
        
        # OpenAI TTS
        self.register_engine(
            TTSEngineType.OPENAI_TTS,
            OpenAITTSGenerator,
            validator=self._validate_openai_tts_config
        )
        
        logger.info(f"已注册 {len(self._engines)} 个TTS引擎")
    
    def register_engine(self, 
                       engine_type: TTSEngineType, 
                       engine_class: Type[AudioGenerator],
                       validator: Optional[Callable] = None):
        """
        注册TTS引擎
        
        Args:
            engine_type: 引擎类型
            engine_class: 引擎类
            validator: 配置验证函数
        """
        self._engines[engine_type] = engine_class
        if validator:
            self._engine_validators[engine_type] = validator
        
        logger.debug(f"已注册TTS引擎: {engine_type.value} -> {engine_class.__name__}")
    
    def unregister_engine(self, engine_type: TTSEngineType):
        """
        注销TTS引擎
        
        Args:
            engine_type: 引擎类型
        """
        if engine_type in self._engines:
            del self._engines[engine_type]
        if engine_type in self._engine_validators:
            del self._engine_validators[engine_type]
        
        logger.debug(f"已注销TTS引擎: {engine_type.value}")
    
    def get_registered_engines(self) -> List[TTSEngineType]:
        """获取已注册的引擎列表"""
        return list(self._engines.keys())
    
    def is_engine_registered(self, engine_type: TTSEngineType) -> bool:
        """检查引擎是否已注册"""
        return engine_type in self._engines
    
    def create_engine(self, 
                     engine_type: TTSEngineType, 
                     config: TTSConfig) -> AudioGenerator:
        """
        创建TTS引擎实例
        
        Args:
            engine_type: 引擎类型
            config: TTS配置
            
        Returns:
            TTS引擎实例
            
        Raises:
            AudioGenerationError: 引擎创建失败
        """
        if engine_type not in self._engines:
            raise AudioGenerationError(
                f"未注册的TTS引擎类型: {engine_type.value}",
                error_code=ErrorCodes.AUDIO_ENGINE_UNAVAILABLE,
                context={"engine_type": engine_type.value}
            )
        
        try:
            # 获取引擎特定配置
            engine_config = self._get_engine_specific_config(engine_type, config)
            
            # 验证配置
            if engine_type in self._engine_validators:
                self._engine_validators[engine_type](engine_config)
            
            # 创建引擎实例
            engine_class = self._engines[engine_type]
            engine = engine_class(**engine_config)
            
            logger.debug(f"已创建TTS引擎: {engine_type.value}")
            return engine
            
        except Exception as e:
            logger.error(f"创建TTS引擎失败 ({engine_type.value}): {e}")
            raise AudioGenerationError(
                f"创建TTS引擎失败: {str(e)}",
                error_code=ErrorCodes.AUDIO_ENGINE_UNAVAILABLE,
                context={
                    "engine_type": engine_type.value,
                    "error": str(e)
                }
            )
    
    def _get_engine_specific_config(self, 
                                   engine_type: TTSEngineType, 
                                   config: TTSConfig) -> Dict[str, Any]:
        """
        获取引擎特定的配置
        
        Args:
            engine_type: 引擎类型
            config: 通用TTS配置
            
        Returns:
            引擎特定配置
        """
        # 基础配置
        base_config = {
            "retry_attempts": config.retry_attempts,
            "retry_delay": config.retry_delay
        }
        
        # 引擎特定配置
        if engine_type == TTSEngineType.EDGE_TTS:
            base_config.update(config.edge_tts_config)
        elif engine_type == TTSEngineType.F5_TTS:
            base_config.update(config.f5_tts_config)
        elif engine_type == TTSEngineType.MEGATTS3:
            base_config.update(config.megatts3_config)
        elif engine_type == TTSEngineType.COSYVOICE2:
            base_config.update(config.cosyvoice2_config)
        elif engine_type == TTSEngineType.OPENAI_TTS:
            base_config.update(config.openai_tts_config)
        elif engine_type == TTSEngineType.AZURE_TTS:
            base_config.update(config.azure_tts_config)
        elif engine_type == TTSEngineType.GOOGLE_TTS:
            base_config.update(config.google_tts_config)
        elif engine_type == TTSEngineType.AWS_POLLY:
            base_config.update(config.aws_polly_config)
        elif engine_type == TTSEngineType.CUSTOM:
            base_config.update(config.custom_config)
        
        return base_config
    
    # 配置验证函数
    def _validate_edge_tts_config(self, config: Dict[str, Any]):
        """验证Edge TTS配置"""
        # Edge TTS通常不需要特殊配置
        pass
    
    def _validate_f5_tts_config(self, config: Dict[str, Any]):
        """验证F5-TTS配置"""
        # 可以检查模型路径、设备等
        if "device" in config and config["device"] not in ["cpu", "cuda", "auto"]:
            raise ValueError(f"无效的设备类型: {config['device']}")
    
    def _validate_megatts3_config(self, config: Dict[str, Any]):
        """验证MegaTTS3配置"""
        if "api_url" not in config:
            raise ValueError("MegaTTS3需要配置api_url")
    
    def _validate_cosyvoice2_config(self, config: Dict[str, Any]):
        """验证CosyVoice2配置"""
        if "device" in config and config["device"] not in ["cpu", "cuda", "auto"]:
            raise ValueError(f"无效的设备类型: {config['device']}")
    
    def _validate_openai_tts_config(self, config: Dict[str, Any]):
        """验证OpenAI TTS配置"""
        if "api_key" not in config:
            raise ValueError("OpenAI TTS需要配置api_key")


class TTSEngineManager:
    """TTS引擎管理器"""
    
    def __init__(self, config: TTSConfig):
        """
        初始化引擎管理器
        
        Args:
            config: TTS配置
        """
        self.config = config
        self.factory = TTSEngineFactory()
        self._current_engine: Optional[AudioGenerator] = None
        self._current_engine_type: Optional[TTSEngineType] = None
        
        logger.debug("TTS引擎管理器初始化完成")
    
    async def get_engine(self, engine_type: Optional[TTSEngineType] = None) -> AudioGenerator:
        """
        获取TTS引擎实例
        
        Args:
            engine_type: 指定的引擎类型，如果为None则使用配置中的主要引擎
            
        Returns:
            TTS引擎实例
        """
        # 确定要使用的引擎类型
        target_engine_type = engine_type or self.config.engine_type
        
        # 如果当前引擎类型匹配，直接返回
        if (self._current_engine is not None and 
            self._current_engine_type == target_engine_type):
            return self._current_engine
        
        # 清理旧引擎
        await self._cleanup_current_engine()
        
        # 创建新引擎
        try:
            self._current_engine = self.factory.create_engine(target_engine_type, self.config)
            self._current_engine_type = target_engine_type
            
            logger.info(f"已切换到TTS引擎: {target_engine_type.value}")
            return self._current_engine
            
        except Exception as e:
            logger.error(f"获取TTS引擎失败: {e}")
            
            # 尝试回退引擎
            fallback_engine = await self._try_fallback_engines(target_engine_type)
            if fallback_engine:
                return fallback_engine
            
            # 所有引擎都失败，抛出异常
            raise AudioGenerationError(
                f"无法获取可用的TTS引擎",
                error_code=ErrorCodes.AUDIO_ENGINE_UNAVAILABLE,
                context={"requested_engine": target_engine_type.value}
            )
    
    async def _try_fallback_engines(self, failed_engine: TTSEngineType) -> Optional[AudioGenerator]:
        """
        尝试回退引擎
        
        Args:
            failed_engine: 失败的引擎类型
            
        Returns:
            可用的回退引擎，如果没有则返回None
        """
        # 获取回退引擎列表
        fallback_engines = self.config.fallback_engines.copy()
        
        # 移除失败的引擎
        if failed_engine in fallback_engines:
            fallback_engines.remove(failed_engine)
        
        # 添加默认回退引擎
        if TTSEngineType.EDGE_TTS not in fallback_engines and failed_engine != TTSEngineType.EDGE_TTS:
            fallback_engines.append(TTSEngineType.EDGE_TTS)
        
        # 尝试每个回退引擎
        for engine_type in fallback_engines:
            try:
                logger.info(f"尝试回退引擎: {engine_type.value}")
                engine = self.factory.create_engine(engine_type, self.config)
                
                self._current_engine = engine
                self._current_engine_type = engine_type
                
                logger.info(f"成功回退到引擎: {engine_type.value}")
                return engine
                
            except Exception as e:
                logger.warning(f"回退引擎失败 ({engine_type.value}): {e}")
                continue
        
        logger.error("所有回退引擎都失败")
        return None
    
    async def switch_engine(self, engine_type: TTSEngineType) -> bool:
        """
        切换TTS引擎
        
        Args:
            engine_type: 目标引擎类型
            
        Returns:
            切换是否成功
        """
        try:
            await self.get_engine(engine_type)
            return True
        except Exception as e:
            logger.error(f"切换引擎失败: {e}")
            return False
    
    def get_current_engine_type(self) -> Optional[TTSEngineType]:
        """获取当前引擎类型"""
        return self._current_engine_type
    
    def get_available_engines(self) -> List[TTSEngineType]:
        """获取可用的引擎列表"""
        return self.factory.get_registered_engines()
    
    async def _cleanup_current_engine(self):
        """清理当前引擎"""
        if self._current_engine is not None:
            try:
                await self._current_engine.cleanup()
            except Exception as e:
                logger.warning(f"清理引擎时出错: {e}")
            finally:
                self._current_engine = None
                self._current_engine_type = None
    
    async def cleanup(self):
        """清理所有资源"""
        await self._cleanup_current_engine()
        logger.debug("TTS引擎管理器已清理")


# 全局工厂实例
_global_factory = TTSEngineFactory()


def get_engine_factory() -> TTSEngineFactory:
    """获取全局TTS引擎工厂实例"""
    return _global_factory


def create_engine_manager(config: TTSConfig) -> TTSEngineManager:
    """
    创建TTS引擎管理器
    
    Args:
        config: TTS配置
        
    Returns:
        引擎管理器实例
    """
    return TTSEngineManager(config)
