"""
MegaTTS3音频生成器实现

MegaTTS3是一个高性能的TTS模型，支持多语言和高质量语音合成。
"""

import io
import time
import hashlib
import logging
import asyncio
import json
from typing import Optional, Dict, Any
import aiohttp

from ..core.interfaces import AudioGenerator
from ..core.models import AudioData
from ..core.exceptions import AudioGenerationError, ErrorCodes

logger = logging.getLogger(__name__)


class MegaTTS3Generator(AudioGenerator):
    """MegaTTS3音频生成器"""
    
    def __init__(self, 
                 api_url: str = "http://localhost:8080/api/tts",
                 api_key: Optional[str] = None,
                 model_name: str = "megatts3-base",
                 timeout: float = 30.0,
                 retry_attempts: int = 3, 
                 retry_delay: float = 0.5,
                 **kwargs):
        """
        初始化MegaTTS3生成器
        
        Args:
            api_url: MegaTTS3 API地址
            api_key: API密钥
            model_name: 模型名称
            timeout: 请求超时时间
            retry_attempts: 重试次数
            retry_delay: 重试延迟
            **kwargs: 其他配置参数
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.timeout = timeout
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        self.config = kwargs
        
        # HTTP会话
        self._session = None
        self._initialized = False
        
        logger.debug(f"MegaTTS3生成器初始化完成，API: {api_url}")
    
    async def _initialize(self):
        """初始化HTTP会话"""
        if self._initialized:
            return
        
        try:
            # 创建HTTP会话
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(timeout=timeout)
            
            # 测试API连接
            await self._test_connection()
            
            self._initialized = True
            logger.info("MegaTTS3生成器初始化完成")
            
        except Exception as e:
            logger.error(f"MegaTTS3初始化失败: {e}")
            raise AudioGenerationError(
                f"MegaTTS3初始化失败: {str(e)}",
                error_code=ErrorCodes.AUDIO_ENGINE_UNAVAILABLE,
                context={"api_url": self.api_url, "model_name": self.model_name}
            )
    
    async def _test_connection(self):
        """测试API连接"""
        try:
            headers = {}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            # 发送健康检查请求
            health_url = self.api_url.replace("/api/tts", "/health")
            async with self._session.get(health_url, headers=headers) as response:
                if response.status == 200:
                    logger.debug("MegaTTS3 API连接正常")
                else:
                    logger.warning(f"MegaTTS3 API响应异常: {response.status}")
                    
        except Exception as e:
            logger.warning(f"MegaTTS3 API连接测试失败: {e}")
            # 连接测试失败不阻止初始化，可能是健康检查端点不存在
    
    async def generate_audio(self, text: str, voice: str, rate: str, volume: str) -> Optional[AudioData]:
        """
        生成音频数据
        
        Args:
            text: 要转换的文本
            voice: 语音名称
            rate: 语速
            volume: 音量
            
        Returns:
            生成的音频数据，失败时返回None
        """
        if not text.strip():
            logger.warning("文本为空，跳过音频生成")
            return None
        
        # 确保已初始化
        await self._initialize()
        
        # 记录开始时间
        start_time = time.time()
        
        # 重试机制
        for attempt in range(self.retry_attempts):
            try:
                logger.debug(f"MegaTTS3生成音频: '{text[:50]}{'...' if len(text) > 50 else ''}'")
                
                # 调用MegaTTS3 API
                audio_data = await self._call_megatts3_api(text, voice, rate, volume)
                
                if audio_data is None:
                    logger.warning(f"MegaTTS3未生成音频数据: '{text[:20]}{'...' if len(text) > 20 else ''}'")
                    if attempt < self.retry_attempts - 1:
                        logger.info(f"重试 ({attempt + 1}/{self.retry_attempts})...")
                        await asyncio.sleep(self.retry_delay)
                        continue
                    return None
                
                # 创建音频数据对象
                result = AudioData(
                    data=audio_data,
                    format="wav",  # MegaTTS3通常输出WAV格式
                    sample_rate=22050,  # 默认采样率
                    text=text,
                    timestamp=time.time(),
                    metadata={
                        "voice": voice,
                        "rate": rate,
                        "volume": volume,
                        "generation_time": time.time() - start_time,
                        "attempt": attempt + 1,
                        "engine": "megatts3",
                        "model_name": self.model_name,
                        "api_url": self.api_url
                    }
                )
                
                logger.debug(f"MegaTTS3音频生成成功: '{text[:20]}{'...' if len(text) > 20 else ''}', 耗时: {time.time() - start_time:.3f}秒")
                return result
                
            except Exception as e:
                logger.error(f"MegaTTS3音频生成错误 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    logger.info(f"重试 ({attempt + 1}/{self.retry_attempts})...")
                    await asyncio.sleep(self.retry_delay)
                else:
                    raise AudioGenerationError(
                        f"MegaTTS3音频生成失败，已重试 {self.retry_attempts} 次: {str(e)}",
                        text=text,
                        voice=voice,
                        error_code=ErrorCodes.AUDIO_GENERATION_FAILED,
                        context={
                            "voice": voice,
                            "rate": rate,
                            "volume": volume,
                            "attempts": self.retry_attempts,
                            "engine": "megatts3",
                            "api_url": self.api_url
                        }
                    )
        
        return None
    
    async def _call_megatts3_api(self, text: str, voice: str, rate: str, volume: str) -> Optional[io.BytesIO]:
        """
        调用MegaTTS3 API生成音频
        
        Args:
            text: 文本内容
            voice: 语音标识
            rate: 语速
            volume: 音量
            
        Returns:
            音频数据流
        """
        try:
            # 准备请求数据
            request_data = {
                "text": text,
                "voice": voice,
                "model": self.model_name,
                "speed": self._parse_rate(rate),
                "volume": self._parse_volume(volume),
                "format": "wav",
                "sample_rate": 22050
            }
            
            # 准备请求头
            headers = {
                "Content-Type": "application/json"
            }
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            # 发送请求
            async with self._session.post(
                self.api_url,
                json=request_data,
                headers=headers
            ) as response:
                
                if response.status == 200:
                    # 读取音频数据
                    audio_bytes = await response.read()
                    audio_buffer = io.BytesIO(audio_bytes)
                    return audio_buffer
                else:
                    error_text = await response.text()
                    logger.error(f"MegaTTS3 API错误 {response.status}: {error_text}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error("MegaTTS3 API请求超时")
            return None
        except Exception as e:
            logger.error(f"MegaTTS3 API调用出错: {e}")
            raise
    
    def _parse_rate(self, rate: str) -> float:
        """解析语速参数"""
        try:
            if rate.startswith('+'):
                return 1.0 + float(rate[1:-1]) / 100.0
            elif rate.startswith('-'):
                return 1.0 - float(rate[1:-1]) / 100.0
            else:
                return float(rate.rstrip('%')) / 100.0
        except:
            return 1.0
    
    def _parse_volume(self, volume: str) -> float:
        """解析音量参数"""
        try:
            if volume.startswith('+'):
                return 1.0 + float(volume[1:-1]) / 100.0
            elif volume.startswith('-'):
                return 1.0 - float(volume[1:-1]) / 100.0
            else:
                return float(volume.rstrip('%')) / 100.0
        except:
            return 1.0
    
    async def warmup(self, text: str, voice: str, rate: str, volume: str):
        """
        预热音频引擎，减少首次延迟
        
        Args:
            text: 预热文本
            voice: 语音名称
            rate: 语速
            volume: 音量
        """
        try:
            logger.debug(f"开始预热 MegaTTS3 引擎，文本: '{text}'")
            start_time = time.time()
            
            # 确保已初始化
            await self._initialize()
            
            # 生成一小段音频进行预热
            await self._call_megatts3_api(text, voice, rate, volume)
            
            elapsed = time.time() - start_time
            logger.debug(f"MegaTTS3 引擎预热完成，耗时: {elapsed:.3f}秒")
            
        except Exception as e:
            logger.debug(f"MegaTTS3预热过程中出现错误: {e}")
            # 预热失败不抛出异常，只记录日志
    
    def generate_cache_key(self, text: str, voice: str, rate: str, volume: str) -> str:
        """
        生成缓存键
        
        Args:
            text: 文本内容
            voice: 语音名称
            rate: 语速
            volume: 音量
            
        Returns:
            缓存键
        """
        # 使用文本、语音、语速、音量和引擎类型生成缓存键
        key_data = f"megatts3|{text}|{voice}|{rate}|{volume}|{self.model_name}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取生成器统计信息"""
        return {
            "generator_type": "MegaTTS3",
            "api_url": self.api_url,
            "model_name": self.model_name,
            "timeout": self.timeout,
            "initialized": self._initialized,
            "retry_attempts": self.retry_attempts,
            "retry_delay": self.retry_delay,
            "config": self.config
        }
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self._session is not None:
                await self._session.close()
                self._session = None
            
            self._initialized = False
            logger.debug("MegaTTS3生成器资源已清理")
            
        except Exception as e:
            logger.error(f"MegaTTS3清理资源时出错: {e}")
