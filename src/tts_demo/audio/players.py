"""
音频播放器实现

提供各种音频播放器的实现。
"""

import time
import logging
import asyncio
from typing import Optional

import pygame

from ..core.interfaces import AudioPlayer
from ..core.models import AudioData, PlaybackState
from ..core.exceptions import AudioPlaybackError, ErrorCodes

logger = logging.getLogger(__name__)


class PygameAudioPlayer(AudioPlayer):
    """Pygame音频播放器"""
    
    def __init__(self, frequency: int = 24000):
        self.frequency = frequency
        self.current_audio: Optional[AudioData] = None
        self.state = PlaybackState.STOPPED
        self._initialized = False
        logger.debug(f"Pygame音频播放器初始化，采样率: {frequency}Hz")
    
    def _ensure_initialized(self):
        """确保pygame mixer已初始化"""
        if not self._initialized:
            try:
                pygame.mixer.init(frequency=self.frequency)
                self._initialized = True
                logger.debug("Pygame mixer初始化完成")
            except Exception as e:
                raise AudioPlaybackError(
                    f"Pygame mixer初始化失败: {str(e)}",
                    error_code=ErrorCodes.AUDIO_DEVICE_UNAVAILABLE,
                    context={"frequency": self.frequency}
                )
    
    async def play(self, audio_data: AudioData) -> bool:
        """播放音频
        
        Args:
            audio_data: 要播放的音频数据
            
        Returns:
            播放是否成功
        """
        if not audio_data or not audio_data.data:
            logger.warning("音频数据为空，跳过播放")
            return False
        
        try:
            self._ensure_initialized()
            
            # 停止当前播放
            if self.is_playing():
                pygame.mixer.music.stop()
            
            # 记录播放开始时间
            play_start_time = time.time()
            
            # 加载并播放音频
            audio_data.data.seek(0)
            pygame.mixer.music.load(audio_data.data)
            pygame.mixer.music.play()
            
            self.current_audio = audio_data
            self.state = PlaybackState.PLAYING
            
            logger.debug(f"开始播放音频: '{audio_data.text[:20]}{'...' if len(audio_data.text) > 20 else ''}'")
            
            # 等待播放完成
            while pygame.mixer.music.get_busy() and self.state == PlaybackState.PLAYING:
                await asyncio.sleep(0.005)  # 5ms检查间隔
            
            # 更新音频时长
            if self.state == PlaybackState.PLAYING:
                audio_data.duration = time.time() - play_start_time
                self.state = PlaybackState.STOPPED
                logger.debug(f"音频播放完成，时长: {audio_data.duration:.2f}秒")
            
            self.current_audio = None
            return True
            
        except Exception as e:
            self.state = PlaybackState.ERROR
            self.current_audio = None
            raise AudioPlaybackError(
                f"音频播放失败: {str(e)}",
                audio_info=f"文本: {audio_data.text[:50]}{'...' if len(audio_data.text) > 50 else ''}",
                error_code=ErrorCodes.AUDIO_PLAYBACK_FAILED,
                context={
                    "text_length": len(audio_data.text),
                    "audio_size": audio_data.get_size_bytes(),
                    "format": audio_data.format
                }
            )
    
    def stop(self):
        """停止播放"""
        try:
            if self._initialized and pygame.mixer.music.get_busy():
                pygame.mixer.music.stop()
                logger.debug("音频播放已停止")
            
            self.state = PlaybackState.STOPPED
            self.current_audio = None
        except Exception as e:
            logger.error(f"停止播放失败: {e}")
            self.state = PlaybackState.ERROR
    
    def pause(self):
        """暂停播放"""
        try:
            if self._initialized and self.state == PlaybackState.PLAYING:
                pygame.mixer.music.pause()
                self.state = PlaybackState.PAUSED
                logger.debug("音频播放已暂停")
        except Exception as e:
            logger.error(f"暂停播放失败: {e}")
            self.state = PlaybackState.ERROR
    
    def resume(self):
        """恢复播放"""
        try:
            if self._initialized and self.state == PlaybackState.PAUSED:
                pygame.mixer.music.unpause()
                self.state = PlaybackState.PLAYING
                logger.debug("音频播放已恢复")
        except Exception as e:
            logger.error(f"恢复播放失败: {e}")
            self.state = PlaybackState.ERROR
    
    def is_playing(self) -> bool:
        """检查是否正在播放"""
        if not self._initialized:
            return False
        
        try:
            return pygame.mixer.music.get_busy() and self.state == PlaybackState.PLAYING
        except Exception:
            return False
    
    def set_volume(self, volume: float):
        """设置音量
        
        Args:
            volume: 音量值（0.0-1.0）
        """
        try:
            if self._initialized:
                volume = max(0.0, min(1.0, volume))  # 限制范围
                pygame.mixer.music.set_volume(volume)
                logger.debug(f"音量设置为: {volume}")
        except Exception as e:
            logger.error(f"设置音量失败: {e}")
    
    def get_state(self) -> PlaybackState:
        """获取当前播放状态"""
        return self.state
    
    def get_current_audio(self) -> Optional[AudioData]:
        """获取当前播放的音频数据"""
        return self.current_audio
    
    def cleanup(self):
        """清理资源"""
        try:
            self.stop()
            if self._initialized:
                pygame.mixer.quit()
                self._initialized = False
                logger.debug("Pygame mixer已清理")
        except Exception as e:
            logger.error(f"清理Pygame mixer失败: {e}")
    
    def get_stats(self) -> dict:
        """获取播放器统计信息"""
        return {
            "player_type": "Pygame",
            "frequency": self.frequency,
            "initialized": self._initialized,
            "state": self.state.value,
            "is_playing": self.is_playing(),
            "current_audio": {
                "text": self.current_audio.text[:50] + "..." if self.current_audio and len(self.current_audio.text) > 50 else self.current_audio.text if self.current_audio else None,
                "duration": self.current_audio.duration if self.current_audio else None,
                "size_bytes": self.current_audio.get_size_bytes() if self.current_audio else None
            } if self.current_audio else None
        }
