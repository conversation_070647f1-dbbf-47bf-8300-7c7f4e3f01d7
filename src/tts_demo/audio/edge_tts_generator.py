"""
Edge TTS音频生成器实现

使用Microsoft Edge TTS进行语音合成。
"""

import io
import time
import hashlib
import logging
import asyncio
from typing import Optional, Dict, Any

import edge_tts

from ..core.interfaces import AudioGenerator
from ..core.models import AudioData
from ..core.exceptions import AudioGenerationError, ErrorCodes

logger = logging.getLogger(__name__)


class EdgeTTSGenerator(AudioGenerator):
    """Edge TTS音频生成器"""
    
    def __init__(self, retry_attempts: int = 3, retry_delay: float = 0.5, **kwargs):
        """
        初始化Edge TTS生成器
        
        Args:
            retry_attempts: 重试次数
            retry_delay: 重试延迟
            **kwargs: 其他配置参数
        """
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        self.config = kwargs
        logger.debug(f"Edge TTS生成器初始化完成，重试次数: {retry_attempts}")
    
    async def generate_audio(self, text: str, voice: str, rate: str, volume: str) -> Optional[AudioData]:
        """
        生成音频数据
        
        Args:
            text: 要转换的文本
            voice: 语音名称
            rate: 语速
            volume: 音量
            
        Returns:
            生成的音频数据，失败时返回None
        """
        if not text.strip():
            logger.warning("文本为空，跳过音频生成")
            return None
        
        # 记录开始时间
        start_time = time.time()
        
        # 重试机制
        for attempt in range(self.retry_attempts):
            try:
                logger.debug(f"Edge TTS生成音频: '{text[:50]}{'...' if len(text) > 50 else ''}'")
                
                # 创建通信对象
                communicate = edge_tts.Communicate(text, voice, rate=rate, volume=volume)
                
                # 使用流式处理方法
                audio_buffer = io.BytesIO()
                received_audio = False
                
                # 从流中收集音频数据
                async for chunk in communicate.stream():
                    if chunk["type"] == "audio":
                        audio_buffer.write(chunk["data"])
                        received_audio = True
                
                # 检查是否收到了音频数据
                if not received_audio:
                    logger.warning(f"Edge TTS未生成音频数据: '{text[:20]}{'...' if len(text) > 20 else ''}'")
                    if attempt < self.retry_attempts - 1:
                        logger.info(f"重试 ({attempt + 1}/{self.retry_attempts})...")
                        await asyncio.sleep(self.retry_delay)
                        continue
                    return None
                
                # 重置指针到开始位置
                audio_buffer.seek(0)
                
                # 创建音频数据对象
                result = AudioData(
                    data=audio_buffer,
                    format="mp3",
                    sample_rate=24000,
                    text=text,
                    timestamp=time.time(),
                    metadata={
                        "voice": voice,
                        "rate": rate,
                        "volume": volume,
                        "generation_time": time.time() - start_time,
                        "attempt": attempt + 1,
                        "engine": "edge_tts"
                    }
                )
                
                logger.debug(f"Edge TTS音频生成成功: '{text[:20]}{'...' if len(text) > 20 else ''}', 耗时: {time.time() - start_time:.3f}秒")
                return result
                
            except Exception as e:
                logger.error(f"Edge TTS音频生成错误 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    logger.info(f"重试 ({attempt + 1}/{self.retry_attempts})...")
                    await asyncio.sleep(self.retry_delay)
                else:
                    raise AudioGenerationError(
                        f"Edge TTS音频生成失败，已重试 {self.retry_attempts} 次: {str(e)}",
                        text=text,
                        voice=voice,
                        error_code=ErrorCodes.AUDIO_GENERATION_FAILED,
                        context={
                            "voice": voice,
                            "rate": rate,
                            "volume": volume,
                            "attempts": self.retry_attempts,
                            "engine": "edge_tts"
                        }
                    )
        
        return None
    
    async def warmup(self, text: str, voice: str, rate: str, volume: str):
        """
        预热音频引擎，减少首次延迟
        
        Args:
            text: 预热文本
            voice: 语音名称
            rate: 语速
            volume: 音量
        """
        try:
            logger.debug(f"开始预热 Edge TTS 引擎，文本: '{text}'")
            start_time = time.time()
            
            # 创建通信对象
            communicate = edge_tts.Communicate(text, voice, rate=rate, volume=volume)
            
            # 使用流式处理方法但不保存结果
            async for _ in communicate.stream():
                pass
            
            elapsed = time.time() - start_time
            logger.debug(f"Edge TTS 引擎预热完成，耗时: {elapsed:.3f}秒")
            
        except Exception as e:
            logger.debug(f"Edge TTS预热过程中出现错误: {e}")
            # 预热失败不抛出异常，只记录日志
    
    def generate_cache_key(self, text: str, voice: str, rate: str, volume: str) -> str:
        """
        生成缓存键
        
        Args:
            text: 文本内容
            voice: 语音名称
            rate: 语速
            volume: 音量
            
        Returns:
            缓存键
        """
        # 使用文本、语音、语速、音量和引擎类型生成缓存键
        key_data = f"edge_tts|{text}|{voice}|{rate}|{volume}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取生成器统计信息"""
        return {
            "generator_type": "Edge TTS",
            "retry_attempts": self.retry_attempts,
            "retry_delay": self.retry_delay,
            "config": self.config
        }
    
    async def cleanup(self):
        """清理资源"""
        # Edge TTS不需要特殊的清理操作
        logger.debug("Edge TTS生成器资源已清理")
