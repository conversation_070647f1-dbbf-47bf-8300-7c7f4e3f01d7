"""
模块化流式TTS引擎

重构后的TTS引擎，采用模块化设计和事件驱动架构。
"""

import asyncio
import logging
import time
from typing import AsyncGenerator, Optional, Callable, Any, List

from ..core.interfaces import TTSEngine, TextProcessor, AudioGenerator, AudioPlayer, CacheManager, QueueManager, EventBus
from ..core.models import PlaybackState, ProcessingStats, TextChunk, AudioData, BreakType, TTSConfig
from ..core.events import (
    SimpleEventBus, TextChunkEvent, AudioGeneratedEvent, PlaybackStateEvent,
    StatsUpdateEvent, ErrorEvent, CacheEvent, QueueEvent
)
from ..core.exceptions import TTSError, create_error_from_exception
from ..processors import OptimizedTextProcessor, SmartTextSplitter, TextSplitterConfig
from ..audio import EdgeTTSGenerator, PygameAudioPlayer, SmartAudioCache
from ..queues import AdaptiveQueueManager
from ..config.models import FirstChunkBreakMode

logger = logging.getLogger(__name__)


class ModularStreamTTSEngine(TTSEngine):
    """模块化流式TTS引擎"""

    def __init__(self, config: TTSConfig,
                 text_processor: Optional[TextProcessor] = None,
                 audio_generator: Optional[AudioGenerator] = None,
                 audio_player: Optional[AudioPlayer] = None,
                 cache_manager: Optional[CacheManager] = None,
                 event_bus: Optional[EventBus] = None):
        """初始化TTS引擎

        Args:
            config: TTS配置
            text_processor: 文本处理器（可选，默认使用OptimizedTextProcessor）
            audio_generator: 音频生成器（可选，默认使用EdgeTTSGenerator）
            audio_player: 音频播放器（可选，默认使用PygameAudioPlayer）
            cache_manager: 缓存管理器（可选，默认使用SmartAudioCache）
            event_bus: 事件总线（可选，默认使用SimpleEventBus）
        """
        self.config = config

        # 初始化组件
        self.text_processor = text_processor or OptimizedTextProcessor(
            debug_mode=config.debug_mode,
            cache_size=config.cache_size
        )

        self.audio_generator = audio_generator or EdgeTTSGenerator(
            retry_attempts=config.retry_attempts,
            retry_delay=config.retry_delay
        )

        self.audio_player = audio_player or PygameAudioPlayer(
            frequency=config.audio_frequency
        )

        self.cache_manager = cache_manager or SmartAudioCache(
            max_items=config.cache_size,
            max_memory_mb=config.cache_memory_mb
        ) if config.enable_cache else None

        self.event_bus = event_bus or SimpleEventBus()

        # 初始化队列
        self.text_queue = AdaptiveQueueManager(
            initial_size=config.queue_size,
            max_size=config.max_queue_size,
            name="text_queue"
        )

        self.audio_queue = AdaptiveQueueManager(
            initial_size=config.queue_size,
            max_size=config.max_queue_size,
            name="audio_queue"
        )

        # 初始化文本分割器
        splitter_config = TextSplitterConfig(
            sentence_ends=['。', '！', '？', '…', '\n\n'],
            clause_breaks=['，', '；', '：', ',', ';', ':'],
            other_breaks=[' ', '、', '(', '（', ')', '）'],
            first_chunk_size=config.first_chunk_size,
            min_sentence_size=config.min_sentence_size,
            max_chunk_size=config.max_chunk_size
        )
        self.text_splitter = SmartTextSplitter(self.text_processor, splitter_config)

        # 状态管理
        self.state = PlaybackState.STOPPED
        self.is_running = False
        self.stats = ProcessingStats()

        # 任务管理
        self.tasks: List[asyncio.Task] = []

        # 文本处理状态
        self.buffer = ""
        self.first_chunk = True

        logger.info(f"模块化TTS引擎初始化完成，配置: {config}")

    async def start(self):
        """启动TTS引擎"""
        if self.is_running:
            logger.warning("TTS引擎已经在运行中")
            return

        logger.info("启动模块化TTS引擎")
        self.is_running = True
        self.stats.reset()

        # 启动事件总线
        await self.event_bus.start()

        # 启动工作任务
        self.tasks = [
            asyncio.create_task(self._text_processor_worker()),
            asyncio.create_task(self._audio_generator_worker()),
            asyncio.create_task(self._audio_player_worker()),
        ]

        # 启动统计更新任务
        if self.config.stats_update_interval > 0:
            self.tasks.append(asyncio.create_task(self._stats_updater()))

        # 预热音频生成器
        asyncio.create_task(self._warmup())

        # 更新状态
        await self._update_state(PlaybackState.PLAYING)

        logger.info("模块化TTS引擎启动完成")

    async def stop(self):
        """停止TTS引擎"""
        if not self.is_running:
            return

        logger.info("停止模块化TTS引擎")
        self.is_running = False

        # 停止音频播放
        self.audio_player.stop()

        # 取消所有任务
        for task in self.tasks:
            if not task.done():
                task.cancel()

        # 等待任务完成
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)

        # 清理队列
        await self.text_queue.clear()
        await self.audio_queue.clear()

        # 清理缓存
        if self.cache_manager:
            self.cache_manager.clear()
        self.text_processor.clear_cache()

        # 停止事件总线
        await self.event_bus.stop()

        # 清理音频播放器
        self.audio_player.cleanup()

        # 更新状态
        await self._update_state(PlaybackState.STOPPED)

        logger.info("模块化TTS引擎已停止")

    async def process_stream(self, stream: AsyncGenerator[Any, None],
                           content_extractor: Callable[[Any], Optional[str]]):
        """处理流式输入"""
        # 重置状态
        self.first_chunk = True
        self.buffer = ""

        # 记录开始时间
        process_start_time = time.time()
        first_token_received = False

        try:
            await self._update_state(PlaybackState.PLAYING)

            # 处理流
            chunk_count = 0
            async for chunk in stream:
                if not self.is_running:
                    logger.info("检测到停止信号，中断流处理")
                    break

                # 提取文本内容
                delta_content = content_extractor(chunk)
                if delta_content is None:
                    continue

                # 记录第一个token时间
                if not first_token_received and delta_content.strip():
                    first_token_received = True
                    self.stats.first_token_time = time.time()

                # 累积文本到缓冲区
                self.buffer += delta_content
                chunk_count += 1

                # 处理缓冲区
                await self._process_buffer()

                if chunk_count % 10 == 0:
                    logger.debug(f"已处理 {chunk_count} 个文本块")

            # 处理剩余文本
            if self.is_running and self.buffer:
                await self._process_remaining_buffer()

            # 等待处理完成
            if self.is_running:
                await self._wait_for_completion()

                total_time = time.time() - process_start_time
                logger.info(f"流处理完成，总耗时: {total_time:.2f}秒")

                # 发送统计事件
                await self.event_bus.emit(StatsUpdateEvent(stats=self.stats))

        except Exception as e:
            error = create_error_from_exception(e)
            await self.event_bus.emit(ErrorEvent(error=error, context="process_stream"))
            raise error

    def get_state(self) -> PlaybackState:
        """获取当前状态"""
        return self.state

    def get_stats(self) -> ProcessingStats:
        """获取统计信息"""
        return self.stats

    async def _update_state(self, new_state: PlaybackState):
        """更新播放状态"""
        if self.state != new_state:
            old_state = self.state
            self.state = new_state
            await self.event_bus.emit(PlaybackStateEvent(old_state=old_state, new_state=new_state))

    async def _process_buffer(self):
        """处理缓冲区文本"""
        if not self.is_running:
            return

        # 使用智能分割器处理文本
        split_text, remaining_text, break_type = self.text_splitter.split_text_smart(
            self.buffer,
            is_first_chunk=self.first_chunk,
            break_mode=self.config.first_chunk_break_mode
        )

        if split_text:
            # 过滤文本
            filtered_text = self.text_processor.filter_content(split_text)
            if filtered_text.strip():
                # 创建文本块
                text_chunk = TextChunk(
                    content=filtered_text,
                    break_type=break_type,
                    position=len(self.buffer) - len(remaining_text)
                )

                # 发送到文本队列
                await self.text_queue.put(text_chunk)

                # 发送事件
                await self.event_bus.emit(TextChunkEvent(chunk=text_chunk))

                # 更新统计
                self.stats.total_chunks += 1
                self.stats.total_characters += len(filtered_text)

                if self.first_chunk:
                    self.first_chunk = False
                    logger.debug(f"首块处理完成: '{filtered_text[:20]}...'")

            # 更新缓冲区
            self.buffer = remaining_text

    async def _process_remaining_buffer(self):
        """处理剩余缓冲区文本"""
        if self.buffer.strip():
            filtered_text = self.text_processor.filter_content(self.buffer)
            if filtered_text.strip():
                text_chunk = TextChunk(
                    content=filtered_text,
                    break_type=BreakType.FORCED,
                    position=0
                )

                await self.text_queue.put(text_chunk)
                await self.event_bus.emit(TextChunkEvent(chunk=text_chunk))

                self.stats.total_chunks += 1
                self.stats.total_characters += len(filtered_text)

            self.buffer = ""

    async def _text_processor_worker(self):
        """文本处理工作循环"""
        while self.is_running:
            try:
                text_chunk = await self.text_queue.get()
                if not text_chunk:
                    self.text_queue.task_done()
                    continue

                # 生成缓存键
                cache_key = None
                if self.cache_manager:
                    if hasattr(self.audio_generator, 'generate_cache_key'):
                        cache_key = self.audio_generator.generate_cache_key(
                            text_chunk.content, self.config.voice, self.config.rate, self.config.volume
                        )
                    else:
                        # 简单的缓存键生成
                        import hashlib
                        key_data = f"{text_chunk.content}|{self.config.voice}|{self.config.rate}|{self.config.volume}"
                        cache_key = hashlib.md5(key_data.encode()).hexdigest()

                    # 尝试从缓存获取
                    cached_audio = self.cache_manager.get(cache_key)
                    if cached_audio:
                        await self.audio_queue.put(cached_audio)
                        await self.event_bus.emit(CacheEvent(action="hit", key=cache_key))
                        self.stats.cache_hits += 1
                        self.text_queue.task_done()
                        continue

                    await self.event_bus.emit(CacheEvent(action="miss", key=cache_key))
                    self.stats.cache_misses += 1

                # 生成音频
                audio_data = await self.audio_generator.generate_audio(
                    text_chunk.content, self.config.voice, self.config.rate, self.config.volume
                )

                if audio_data:
                    # 添加到缓存
                    if self.cache_manager and cache_key:
                        success = self.cache_manager.put(cache_key, audio_data)
                        if success:
                            await self.event_bus.emit(CacheEvent(action="put", key=cache_key, size=audio_data.get_size_bytes()))

                    # 发送到音频队列
                    await self.audio_queue.put(audio_data)

                    # 发送事件
                    await self.event_bus.emit(AudioGeneratedEvent(audio_data=audio_data, text=text_chunk.content))

                self.text_queue.task_done()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"文本处理工作循环错误: {e}")
                error = create_error_from_exception(e, {"component": "text_processor"})
                await self.event_bus.emit(ErrorEvent(error=error, context="text_processor_worker"))
                self.text_queue.task_done()
                await asyncio.sleep(0.1)

    async def _audio_generator_worker(self):
        """音频生成工作循环（已合并到文本处理工作循环中）"""
        # 这个方法现在是空的，因为音频生成已经合并到文本处理工作循环中
        # 保留这个方法是为了保持接口一致性
        while self.is_running:
            await asyncio.sleep(1.0)

    async def _audio_player_worker(self):
        """音频播放工作循环"""
        first_audio_played = False
        audio_count = 0

        while self.is_running:
            try:
                audio_data = await self.audio_queue.get()
                if not audio_data:
                    self.audio_queue.task_done()
                    continue

                # 记录第一个音频播放时间
                if not first_audio_played:
                    first_audio_played = True
                    self.stats.first_audio_time = time.time()

                    if self.stats.first_token_time > 0:
                        self.stats.time_to_first_audio = self.stats.first_audio_time - self.stats.first_token_time

                    logger.info(f"开始播放第一个音频: {time.strftime('%H:%M:%S', time.localtime(self.stats.first_audio_time))}")

                # 播放音频
                play_start_time = time.time()
                success = await self.audio_player.play(audio_data)

                if success and self.is_running:
                    # 更新统计
                    audio_duration = time.time() - play_start_time
                    self.stats.total_audio_time += audio_duration
                    audio_count += 1

                    logger.debug(f"音频播放完成 #{audio_count}: '{audio_data.text[:20]}...'")

                self.audio_queue.task_done()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"音频播放工作循环错误: {e}")
                error = create_error_from_exception(e, {"component": "audio_player"})
                await self.event_bus.emit(ErrorEvent(error=error, context="audio_player_worker"))
                self.audio_queue.task_done()
                await asyncio.sleep(0.1)

    async def _stats_updater(self):
        """统计信息更新任务"""
        while self.is_running:
            try:
                await asyncio.sleep(self.config.stats_update_interval)
                if self.is_running:
                    await self.event_bus.emit(StatsUpdateEvent(stats=self.stats))
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"统计更新错误: {e}")
                await asyncio.sleep(1.0)

    async def _warmup(self):
        """预热音频生成器"""
        try:
            await self.audio_generator.warmup("准备", self.config.voice, self.config.rate, self.config.volume)
        except Exception as e:
            logger.debug(f"预热失败: {e}")

    async def _wait_for_completion(self):
        """等待所有任务完成"""
        try:
            logger.info("等待所有文本处理任务完成...")
            await self.text_queue.join()

            logger.info("等待所有音频播放任务完成...")
            await self.audio_queue.join()

        except Exception as e:
            logger.error(f"等待任务完成失败: {e}")

    def add_event_handler(self, event_type: str, handler: Callable, async_handler: bool = False):
        """添加事件处理器"""
        self.event_bus.subscribe(event_type, handler, async_handler)

    def remove_event_handler(self, event_type: str, handler: Callable):
        """移除事件处理器"""
        self.event_bus.unsubscribe(event_type, handler)

    def get_component_stats(self) -> dict:
        """获取各组件统计信息"""
        stats = {
            "engine": {
                "state": self.state.value,
                "is_running": self.is_running,
                "buffer_length": len(self.buffer),
                "first_chunk": self.first_chunk
            },
            "text_processor": self.text_processor.get_stats() if hasattr(self.text_processor, 'get_stats') else {},
            "audio_generator": self.audio_generator.get_stats() if hasattr(self.audio_generator, 'get_stats') else {},
            "audio_player": self.audio_player.get_stats() if hasattr(self.audio_player, 'get_stats') else {},
            "text_queue": self.text_queue.get_stats(),
            "audio_queue": self.audio_queue.get_stats(),
            "event_bus": self.event_bus.get_stats() if hasattr(self.event_bus, 'get_stats') else {}
        }

        if self.cache_manager:
            stats["cache_manager"] = self.cache_manager.get_stats()

        return stats
