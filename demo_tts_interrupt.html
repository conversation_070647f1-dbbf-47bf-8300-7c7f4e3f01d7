<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS自动中断功能演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .demo-section {
            margin-bottom: 30px;
        }
        .demo-section h3 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .message-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.info {
            color: #007bff;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.warning {
            color: #ffc107;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .connection-status {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .connection-status.connected {
            background-color: #28a745;
        }
        .connection-status.disconnected {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 TTS自动中断功能演示</h1>
        <p>这个演示展示了新的TTS自动中断功能：当发送新消息时，系统会自动中断上一次的TTS朗读。</p>
        
        <div class="status info">
            <span class="connection-status disconnected" id="connectionStatus"></span>
            <span id="statusText">未连接到服务器</span>
        </div>
    </div>

    <div class="container">
        <div class="demo-section">
            <h3>🎯 功能特性</h3>
            <ul class="feature-list">
                <li>新消息自动中断上一次TTS播放</li>
                <li>即时响应，无明显延迟</li>
                <li>支持WebSocket和SSE两种通信方式</li>
                <li>完善的错误处理和资源清理</li>
                <li>用户友好的视觉反馈</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <div class="demo-section">
            <h3>🧪 测试场景</h3>
            <div class="demo-buttons">
                <button class="btn" onclick="testBasicInterrupt()">基本中断测试</button>
                <button class="btn" onclick="testRapidMessages()">快速连续消息</button>
                <button class="btn" onclick="testLongToShort()">长文本→短文本</button>
                <button class="btn" onclick="testShortToLong()">短文本→长文本</button>
                <button class="btn btn-danger" onclick="clearLog()">清空日志</button>
            </div>
            
            <div>
                <input type="text" class="message-input" id="customMessage" 
                       placeholder="输入自定义消息进行测试..." 
                       onkeypress="handleKeyPress(event)">
                <button class="btn" onclick="sendCustomMessage()">发送自定义消息</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="demo-section">
            <h3>📋 测试日志</h3>
            <div class="log" id="logContainer">
                <div class="log-entry info">等待开始测试...</div>
            </div>
        </div>
    </div>

    <script>
        const sessionId = 'demo-interrupt-' + Date.now();
        const baseUrl = 'http://localhost:8000'; // 修改为实际的服务器地址
        let isConnected = false;

        // 日志记录
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusElement = document.getElementById('connectionStatus');
            const textElement = document.getElementById('statusText');
            
            if (connected) {
                statusElement.className = 'connection-status connected';
                textElement.textContent = '已连接到服务器';
            } else {
                statusElement.className = 'connection-status disconnected';
                textElement.textContent = '未连接到服务器';
            }
        }

        // 发送消息
        async function sendMessage(content) {
            if (!isConnected) {
                addLog('错误：未连接到服务器', 'error');
                return false;
            }

            try {
                addLog(`发送消息: ${content.substring(0, 50)}...`, 'info');
                
                const response = await fetch(`${baseUrl}/api/sessions/${sessionId}/send`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: content
                    })
                });

                if (response.ok) {
                    addLog('消息发送成功', 'success');
                    return true;
                } else {
                    addLog(`消息发送失败: HTTP ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                addLog(`发送消息错误: ${error.message}`, 'error');
                return false;
            }
        }

        // 检查服务器连接
        async function checkConnection() {
            try {
                const response = await fetch(`${baseUrl}/api/sessions`);
                updateConnectionStatus(response.ok);
                if (response.ok) {
                    addLog('服务器连接正常', 'success');
                } else {
                    addLog('服务器响应异常', 'warning');
                }
            } catch (error) {
                updateConnectionStatus(false);
                addLog(`连接检查失败: ${error.message}`, 'error');
            }
        }

        // 测试函数
        async function testBasicInterrupt() {
            addLog('=== 开始基本中断测试 ===', 'info');
            
            // 发送长文本
            await sendMessage('请详细介绍一下人工智能的发展历史，包括从早期的图灵测试到现代的深度学习技术，以及未来可能的发展方向。这需要一个很长很详细的回答。');
            
            // 等待3秒
            addLog('等待3秒让TTS开始播放...', 'info');
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 发送短文本（应该中断上一个）
            await sendMessage('这是第二个消息，应该会立即中断第一个消息的TTS播放。');
            
            addLog('基本中断测试完成 - 请观察TTS是否被正确中断', 'success');
        }

        async function testRapidMessages() {
            addLog('=== 开始快速连续消息测试 ===', 'info');
            
            const messages = [
                '第一个消息：这是一个较长的测试文本。',
                '第二个消息：立即中断上一个。',
                '第三个消息：再次中断。',
                '第四个消息：最后一次中断测试。'
            ];

            for (let i = 0; i < messages.length; i++) {
                await sendMessage(messages[i]);
                if (i < messages.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
            
            addLog('快速连续消息测试完成 - 应该只听到最后一个消息', 'success');
        }

        async function testLongToShort() {
            addLog('=== 开始长文本→短文本测试 ===', 'info');
            
            await sendMessage('这是一个非常长的文本消息，用于测试从长文本到短文本的TTS中断功能。请详细说明相关的技术原理和实现细节，包括各种可能的边界情况和异常处理机制。');
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await sendMessage('短消息。');
            
            addLog('长→短测试完成', 'success');
        }

        async function testShortToLong() {
            addLog('=== 开始短文本→长文本测试 ===', 'info');
            
            await sendMessage('短消息。');
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await sendMessage('这是一个很长的后续消息，用于测试从短文本切换到长文本时的TTS中断和播放效果。系统应该能够正确处理这种切换，确保用户体验的流畅性。');
            
            addLog('短→长测试完成', 'success');
        }

        function sendCustomMessage() {
            const input = document.getElementById('customMessage');
            const message = input.value.trim();
            
            if (message) {
                sendMessage(message);
                input.value = '';
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendCustomMessage();
            }
        }

        function clearLog() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div class="log-entry info">日志已清空</div>';
        }

        // 页面加载时检查连接
        window.addEventListener('load', function() {
            addLog('页面加载完成，检查服务器连接...', 'info');
            checkConnection();
            
            // 定期检查连接状态
            setInterval(checkConnection, 10000);
        });
    </script>
</body>
</html>
